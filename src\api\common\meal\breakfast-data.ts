import { defHttp } from '/@/utils/http/axios';

enum Api {
  listUrl = '/meal/rpt/breakfast/data/getPageList',
  createUrl = '/meal/rpt/breakfast/data/create',
  detailUrl = '/meal/rpt/breakfast/data/detail',
}

export const listAction = (params?: { tabIndex: Number; page: Number; pageSize: Number }) =>
  defHttp.get({ url: Api.listUrl, params });

export const createAction = (params?) => defHttp.post({ url: Api.createUrl, params });

export const detailAction = (params: { id: number }) => defHttp.get({ url: Api.detailUrl, params });
