<template>
  <BasicModal
    @register="registerModal"
    title="审批流程"
    width="100%"
    @ok="handleOk"
    defaultFullscreen
    :showCancelBtn="false"
    :showOkBtn="false"
  >
    <div class="main">
      <div class="left">1</div>
      <div class="right">
        <Steps direction="vertical" size="small">
          <template v-for="item in data" :key="item">
            <Step
              :status="processingStatusValue(item.status)"
              :title="titleVnode(item)"
              :description="descVnode(item)"
              disabled
            >
              <template #icon>
                <template v-if="item.type === 'start'">
                  <UserOutlined />
                </template>

                <template v-else-if="item.type === 'approver'">
                  <TeamOutlined
                    :style="{
                      color: '#FF8C00',
                    }"
                  />
                </template>

                <template v-else-if="item.type === 'notify'">
                  <SendOutlined :style="{ color: '#808000' }" />
                </template>

                <template v-else-if="item.type === 'endpersons'">
                  <HeatMapOutlined
                    :style="{
                      color: 'red',
                    }"
                  />
                </template>
              </template>
            </Step>
          </template>
        </Steps>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Steps, Step } from 'ant-design-vue';
  import { UserOutlined, TeamOutlined, HeatMapOutlined, SendOutlined } from '@ant-design/icons-vue';
  import { processingStatusValue, descVnode, titleVnode } from '../datas/fn';
  import { ref } from 'vue';

  const emit = defineEmits(['register', 'success']);

  let data: any = ref([]);

  // const props = defineProps({
  //   api: {
  //     type: Function
  //   }
  // })

  /** 注册添加流水Modal，注册添加流水Modal，刚进来会触发*/
  const [registerModal, {}] = useModalInner((recordData) => {
    console.log(recordData);
    // 获取数据
    // let a = await props.api()
    data.value = [
      {
        id: 509,
        title: 'Pl管理模块',
        person_id: 201,
        process_main_id: 45,
        allow_btn_text: '同意',
        approve_type: 1,
        receive_time: 1711956458,
        handle_time: 0,
        content: '',
        status: 4,
        type: 'start',
        created_at: '2024-03-30 11:41:54',
      },
      {
        id: 519,
        title: 'Pl订单模块',
        person_id: 202,
        process_main_id: 45,
        allow_btn_text: '通过1',
        approve_type: 1,
        receive_time: 1711956458,
        handle_time: 0,
        content: '',
        type: 'approver',
        status: 4,
        created_at: '2024-03-30 11:41:54',
      },
      {
        id: 509,
        title: 'Pl管理模块',
        person_id: 201,
        process_main_id: 45,
        allow_btn_text: '同意',
        approve_type: 1,
        receive_time: 1711956458,
        handle_time: 0,
        content: '',
        status: 3,
        type: 'notify',
        created_at: '2024-03-30 11:41:54',
      },
      {
        id: 519,
        title: 'Pl订单模块',
        person_id: 202,
        process_main_id: 45,
        allow_btn_text: '通过2',
        approve_type: 2,
        receive_time: 1711956458,
        handle_time: 0,
        content: '',
        type: 'approver',
        status: 2,
        created_at: '2024-03-30 11:41:54',
      },
      {
        id: 509,
        title: 'Pl管理模块',
        person_id: 201,
        process_main_id: 45,
        allow_btn_text: '同意',
        approve_type: 1,
        receive_time: 1711956458,
        handle_time: 0,
        content: '',
        status: 1,
        type: 'endpersons',
        created_at: '2024-03-30 11:41:54',
      },
    ];

    try {
    } catch (error) {
      console.log(error);
    }

    // reload()
  });

  /** 点击确认 */
  const handleOk = () => {
    emit('success', 1);
  };
</script>
<style lang="less" scoped>
  .main {
    display: flex;
    .left {
      width: 70%;
      border: 1px solid #ccc;
    }
    .right {
      width: 30%;
    }
  }
  :deep(.ant-picker) {
    width: 100%;
  }
  :deep(.ant-steps-item-title) {
    width: 100%;
  }
</style>
