<template>
  1
  <!-- 发起人配置 -->
  <!-- <BasicForm @register="registerForm"> </BasicForm> -->
</template>

<script setup name="StartDrawer" lang="ts">
// import { getCurrentInstance, inject } from 'vue'
// import { KEY_PROCESS_DATA } from '../../config/keys'
// import { BasicForm, useForm } from '/@/components/Form/index'

// const props = defineProps({
//   config: {
//     // 传入的流程配置数据
//     type: Object,
//     default: {}
//   }
// })

// const [registerForm, { validate }] = useForm({
//   labelWidth: 90,
//   baseColProps: { span: 24 },
//   showActionButtonGroup: false,
//   schemas: [
//     {
//       field: 'name',
//       label: '用户名',
//       component: 'Input',
//       colProps: { span: 24 }
//     },
//     {
//       field: 'roleName',
//       label: '角色名',
//       component: 'Input',
//       colProps: { span: 24 }
//     }
//   ]
// })

// const { proxy } = getCurrentInstance()
// console.log(proxy)

// 获取流程数据
// const processData = inject(KEY_PROCESS_DATA)
</script>

<style lang="less" scoped></style>
