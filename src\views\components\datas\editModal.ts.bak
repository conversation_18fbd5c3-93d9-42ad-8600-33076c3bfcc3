import {
  getPersonList,
  getPersonList<PERSON><PERSON>t,
  getDeptList,
  getCustomList,
  getBusinessList,
  getProjectList,
  getCountriesList,
} from '@/api/common';
import { cloneDeep, keys } from 'lodash-es';
import { h, ref } from 'vue';
import { optionsFn } from './common';
import { Tinymce } from '@/components/Tinymce';
import { defHttp } from '@/utils/http/axios';
import { uploadApi } from '@/api/sys/upload';
import { div, mul, add, sub } from '@/utils/math';

export const dynamictabConfig = ref({});
export const linkageConfig = ref({});
export const colorConfig = ref({});
export const tableSlotConfig = ref({});

export const schemaFn = (schema: Array<any>, _type = 'add') => {
  const newScheam: any = [];
  schema?.forEach((item) => {
    let schemas: object = {};
    newScheam.push({
      field: item.label,
      label: h(
        'span',
        {
          style: {
            paddingLeft: '10px',
            borderLeft: '5px solid #007aff',
          },
        },
        item.label,
      ),
      component: 'Divider',
      colProps: { span: 24 },
      componentProps: {
        style: {
          fontWeight: 'bold',
          fontSize: '15px',
        },
      },
    });

    item.dataItem.forEach((schemaItem) => {
      const def = {
        field: schemaItem.field,
        label: schemaItem.title ? `${schemaItem.title}:` : '',
        colProps: { span: 12 },
        labelWidth: 150,
        defaultValue: schemaItem?.default,
        dynamicDisabled: _type == 'detail' || (_type == 'edit' && schemaItem?.isedit),
      };

      switch (schemaItem.type) {
        case 'select':
          schemas = {
            component: 'Select',
            componentProps: ({ formActionType }) => {
              let { updateSchema } = formActionType;
              console.log('options:', schemaItem.options);
              return {
                ...schemaItem,
                options: optionsFn(schemaItem.options),
                showSearch: true,
                optionFilterProp: 'label',
                onChange: async (e) => {
                  console.log('onChange:', e);
                  /*新增了条件显示字段的配置和方法*/
                  if (schemaItem.changeFn) {
                    // changeFn是个数组
                    schemaItem.changeFn.forEach((item) => {
                      if (item.type == 'showField') {
                        //item.value是数组
                        if (item.value.indexOf(e) > -1) {
                          console.log('命中:', item.field);
                          updateSchema({
                            field: item.field,
                            ifShow: true,
                          });
                        } else {
                          updateSchema({
                            field: item.field,
                            ifShow: false,
                          });
                        }
                      }
                    });
                  }
                  /*END*/
                },
              };
            },
          };
          break;
        case 'radio':
          schemas = {
            component: 'RadioGroup',
            componentProps: ({ formActionType }) => {
              let { clearValidate, updateSchema } = formActionType;
              return {
                ...schemaItem,
                options: optionsFn(schemaItem.options),
                onChange: (e) => {
                  console.log('onChange:', e);
                },
              };
            },
          };

          break;
        case 'textArea':
          schemas = {
            component: 'InputTextArea',
            componentProps: {
              ...schemaItem,
            },
          };
          break;
        case 'date':
          schemas = {
            component: 'DatePicker',
            componentProps: {
              valueFormat: 'YYYY-MM-DD',
              format: 'YYYY-MM-DD',
              style: { width: '100%' },
              ...schemaItem,
            },
          };
          break;
        case 'time':
          schemas = {
            component: 'TimePicker',
            componentProps: {
              style: { width: '100%' },
              ...schemaItem,
            },
          };
          break;
        case 'datetime':
          schemas = {
            component: 'DatePicker',
            componentProps: {
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              format: 'YYYY-MM-DD HH:mm:ss',
              showTime: true,
              style: { width: '100%' },
              ...schemaItem,
            },
          };
          break;
        case 'rangePicker':
          schemas = {
            component: 'RangePicker',
            componentProps: {
              valueFormat: 'YYYY-MM-DD HH:mm',
              format: 'YYYY-MM-DD HH:mm',
              showTime: true,
              style: { width: '100%' },
              ...schemaItem,
            },
          };
          break;
        case 'number':
          schemas = {
            component: 'InputNumber',
            componentProps: {
              min: schemaItem.min ?? 0,
              ...schemaItem,
            },
          };
          break;
        case 'upload':
          schemas = {
            component: 'ImageUpload',
            componentProps: {
              resultField: 'data.url',
              api: (file, progress) => {
                return new Promise((resolve, reject) => {
                  uploadApi(file, progress)
                    .then((uploadApiResponse) => {
                      // 获取上传后的图片 URL
                      const url = [uploadApiResponse.data.result.path];
                      resolve({
                        code: 200,
                        data: {
                          url, // 逐张添加 URL
                        },
                      });
                    })
                    .catch((error) => {
                      reject(error);
                    });
                });
              },
            },
          };
          break;
        case 'person':
          schemas = {
            component: 'ApiTreeSelect',
            componentProps: {
              ...schemaItem,
              api: getPersonList,
              immediate: true,
              treeSelectProps: {
                mode: 'tags',
                maxTagCount: 50,
                treeDefaultExpandAll: true,
                showSearch: true,
                multiple: schemaItem?.multiple || false,
                treeCheckable: false,
                treeLine: {
                  showLeafIcon: false,
                },
                fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
                placeholder: '请选择!',
                allowClear: true,
                filterTreeNode: (search, item) => {
                  if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
                  return false;
                },
              },
            },
          };
          break;
        case 'personAudit':
          schemas = {
            component: 'ApiTreeSelect',
            componentProps: {
              ...schemaItem,
              api: getPersonListAudit,
              immediate: true,
              treeSelectProps: {
                mode: 'tags',
                maxTagCount: 50,
                treeDefaultExpandAll: true,
                showSearch: true,
                multiple: schemaItem?.multiple || false,
                treeCheckable: false,
                treeLine: {
                  showLeafIcon: false,
                },
                fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
                placeholder: '请选择!',
                allowClear: true,
                filterTreeNode: (search, item) => {
                  if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
                  return false;
                },
              },
            },
          };
          break;
        case 'dept':
          schemas = {
            component: 'ApiTreeSelect',
            componentProps: {
              ...schemaItem,
              api: getDeptList,
              immediate: true,
              treeSelectProps: {
                mode: 'tags',
                maxTagCount: 50,
                treeDefaultExpandAll: true,
                showSearch: true,
                multiple: schemaItem?.multiple || false,
                treeCheckable: false,
                treeLine: {
                  showLeafIcon: false,
                },
                fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
                placeholder: '请选择!',
                allowClear: true,
                filterTreeNode: (search, item) => {
                  if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
                  return false;
                },
              },
            },
          };
          break;
        case 'countries':
          schemas = {
            component: 'ApiSelect',
            componentProps: {
              api: getCountriesList,
              fieldNames: { key: 'key', value: 'id', label: 'name' },
              searchPlaceholder: '请选择！',
              showSearch: true,
              optionFilterProp: 'name',
              mode: schemaItem?.multiple ? 'multiple' : '',
            },
            itemProps: {
              validateTrigger: 'blur',
            },
          };
          break;
        case 'custom':
        case 'business':
        case 'project':
          let api = {
            custom: getCustomList,
            business: getBusinessList,
            project: getProjectList,
          }[schemaItem.type];
          schemas = {
            component: 'PagingApiSelect',
            componentProps: ({ formActionType }) => {
              let { clearValidate } = formActionType;
              return {
                ...schemaItem,
                searchMode: true,
                pagingMode: true,
                pagingSize: 20,
                api,
                resultField: 'result',
                returnParamsField: 'id',
                selectProps: {
                  fieldNames: { key: 'id', value: 'id', label: 'name' },
                  showSearch: true,
                  placeholder: '请选择',
                  optionFilterProp: 'name',
                  allowClear: true,
                  mode: schemaItem?.multiple ? 'multiple' : '',
                },
                onChange: async () => {
                  await clearValidate();
                },
              };
            },
            itemProps: {
              validateTrigger: 'blur',
            },
          };
          break;
        case 'dynamictab':
          schemas = {
            slot: 'dynamictab',
            colProps: { span: 24 },
          };
          // 存储一对多的columns
          dynamictabConfig.value[`${schemaItem.field}`] = {
            columns: schemaItem.cols,
            showAddBtn: schemaItem?.showAddBtn,
          };
          break;
        //是否确认
        case 'sureBox':
          schemas = {
            component: 'CheckboxGroup',
            componentProps: {
              options: [{ label: schemaItem.default, value: true }],
            },
          };
          break;
        case 'selectapi':
          schemas = {
            component: 'PagingApiSelect',
            componentProps: ({ formActionType }) => {
              let { clearValidate, updateSchema } = formActionType;
              console.log('options:', schemaItem.options);
              return {
                ...schemaItem,
                searchMode: true,
                pagingMode: true,
                pagingSize: 20,
                api: (params?) => defHttp.get({ url: schemaItem.api, params }),
                resultField: schemaItem?.resultField ?? 'result',
                returnParamsField: schemaItem?.returnParamsField ?? 'id',
                selectProps: {
                  fieldNames: schemaItem?.FieldConfig ?? {
                    key: 'id',
                    value: 'value',
                    label: 'name',
                  },
                  showSearch: true,
                  placeholder: '请选择',
                  optionFilterProp: 'name',
                  allowClear: true,
                  mode: schemaItem?.multiple ? 'multiple' : '',
                },
                onChange: async (e) => {
                  console.log('onChange:', e);
                  await clearValidate();
                  /*新增了条件显示字段的配置和方法*/
                  if (schemaItem.changeFn) {
                    // changeFn是个数组
                    schemaItem.changeFn.forEach((item) => {
                      if (item.type == 'showField') {
                        //item.value是数组
                        if (item.value.indexOf(e) > -1) {
                          console.log('命中:', item.field);
                          updateSchema({
                            field: item.field,
                            ifShow: true,
                          });
                        } else {
                          updateSchema({
                            field: item.field,
                            ifShow: false,
                          });
                        }
                      }
                    });
                  }
                  /*END*/
                },
              };
            },
            itemProps: {
              validateTrigger: 'blur',
            },
          };
          break;
        case 'treeselectapi':
          schemas = {
            component: 'ApiTreeSelect',
            componentProps: {
              ...schemaItem,
              api: (params?) => defHttp.get({ url: schemaItem.api, params }),
              treeSelectProps: {
                mode: 'tags',
                maxTagCount: 50,
                treeDefaultExpandAll: true,
                showSearch: true,
                multiple: schemaItem?.multiple || false,
                treeCheckable: false,
                treeLine: {
                  showLeafIcon: false,
                },
                fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
                placeholder: '请选择!',
                allowClear: true,
                filterTreeNode: (search, item) => {
                  if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
                  return false;
                },
              },
            },
          };
          break;
        case 'tinymce':
          schemas = {
            component: 'Input',
            render: ({ model, field }) => {
              return h(Tinymce, {
                value: model[field],
                onChange: (value: string) => {
                  model[field] = value;
                },
              });
            },
          };
          break;
        case 'file':
          schemas = {
            slot: 'files',
          };
          break;
        case 'anyfile':
          schemas = {
            slot: 'anyfile',
          };
          break;
        case 'import':
          schemas = {
            slot: 'import',
          };
          break;
        case 'download':
          schemas = {
            slot: 'download',
          };
          break;
        /** 快捷模板 */
        case 'quick':
          schemas = {
            slot: 'quick',
          };
          break;
        case 'radioColor':
          schemas = {
            slot: 'radioColor',
          };
          colorConfig.value[`${schemaItem.field}`] = {
            colorArr: schemaItem.color_arr,
          };
          break;
        /**  左边select，右边pageApiSelect ,左边select的选项会改变pageApiSelect的接口返回 */
        case 'linkage':
          schemas = {
            slot: 'linkage',
          };
          linkageConfig.value[`${schemaItem.field}`] = {
            options: schemaItem.linkage_options,
          };
          break;
        // 销售单号选择，左边选单号，右边自动显示部门
        case 'saleOrderSel': {
          schemas = {
            slot: 'saleOrderSel',
          };
          tableSlotConfig.value[`${schemaItem.field}`] = schemaItem;
          break;
        }
        case 'tableSlot':
          schemas = {
            slot: 'tableSlot',
          };
          // 存储columns
          tableSlotConfig.value[`${schemaItem.field}`] = schemaItem;
          break;
        /** sop 详情 */
        case 'approvalNav':
          schemas = {
            slot: 'approvalNav',
          };
          break;
        // case 'InputGroup':
        //   schemas = {
        //     slot: 'InputGroup',
        //   };
        //   break;
        default:
          schemas = {
            component: 'Input',
            componentProps: {
              ...schemaItem,
            },
            // dynamicRules: () => {
            //   return [
            //     {
            //       required: true,
            //       trigger: ['blur'],
            //       validator: (_, value) => {
            //         return new Promise((resolve, reject) => {
            //           setTimeout(() => {
            //             if (value == 'AA') {
            //               resolve(value);
            //             } else {
            //               reject('已经有该值了！');
            //             }
            //           }, 2000);
            //         });
            //       },
            //     },
            //   ];
            // },
          };
          break;
      }

      // if (schemaItem.field == 'percentagemoney') {
      //   schemaItem.change = {
      //     change_options: {
      //       commission: {
      //         keyName: 'is_strid',
      //         action_type: 'mul', // add, sub , mul,div
      //       },
      //     },
      //     event_type: 'blur',
      //   };
      // }
      // console.log(schemaItem);

      // 联动函数
      if (Reflect.has(schemaItem, 'change')) {
        schemas = linkageFn(schemaItem, schemas);
      }

      newScheam.push({
        ...def,
        ...schemas,
        ...schemaItem,
      });
    });
  });

  // newScheam.push({
  //   field: 'upload',
  //   component: 'ImageUpload',
  //   componentProps: {
  //     resultField: 'data.url',
  //     api: (file, progress) => {
  //       return new Promise((resolve, reject) => {
  //         uploadApi(file, progress)
  //           .then((uploadApiResponse) => {
  //             // 获取上传后的图片 URL
  //             const url = [uploadApiResponse.data.result.path];
  //             resolve({
  //               code: 200,
  //               data: {
  //                 url, // 逐张添加 URL
  //               },
  //             });
  //           })
  //           .catch((error) => {
  //             reject(error);
  //           });
  //       });
  //     },
  //   },
  //   label: 'tinymce',
  // });
  return newScheam;
};

/** 联动函数
 * @schemaItem 当前操作的接口返回的数据
 * @schemas  自己定义需要配补充的

 */

const linkageFn = (schemaItem, schemas): Array<any> => {
  const newScheamItem: any = cloneDeep(schemas);
  const componentProps = ({ formActionType }) => {
    const { clearValidate } = formActionType;
    clearValidate();
    const eventType = schemaItem.change.event_type; // 事件类型
    let returnObj: any = null;
    console.log(eventType);

    if (eventType == 'blur') {
      returnObj = {
        onBlur: (val, obj) => {
          commonLinkageFn(schemaItem, formActionType, { val, obj });
        },
      };
    } else {
      returnObj = {
        onChange: async (val, obj) => {
          commonLinkageFn(schemaItem, formActionType, { val, obj });
        },
      };
    }
    return {
      ...returnObj,
      ...schemas?.componentProps,
    };
  };

  newScheamItem.componentProps = componentProps;

  return newScheamItem;
};
/**
 *
 * @param schemaItem
 * @param formActionType 表单方法
 * @param inputVal 事件触发的值
 */
const commonLinkageFn = (schemaItem, formActionType, inputVal) => {
  const { getFieldsValue, updateSchema, setFieldsValue } = formActionType;
  const changeOptions = schemaItem.change.change_options; // 需要联动的配置;
  const ifHaveOptions = schemaItem.change.haveOptions; // 是否是下拉有选项的
  let count = 0;

  // 遍历需要联动的field
  keys(changeOptions).forEach((field) => {
    // 联动塞值 查看配置信息是否有keyName这个属性，如果有则说明是要塞值的
    if (Reflect.has(changeOptions[field], 'keyName')) {
      if (inputVal.obj) {
        setFieldsValue({
          [field]: `${inputVal.obj[changeOptions[field]['keyName']]}`,
        });
      } else {
        // 这里处理的是直接输入值的情况
        let data = getFieldsValue()[changeOptions[field]['keyName']];
        if (!getFieldsValue()[field] && inputVal.val?.target?.value) {
          if (
            Reflect.has(changeOptions[field], 'action_type') &&
            Number(inputVal.val?.target?.value)
          ) {
            switch (changeOptions[field]['action_type']) {
              case 'add':
                data = add(
                  inputVal.val?.target?.value,
                  getFieldsValue()[changeOptions[field]['keyName']],
                  4,
                );
                break;
              case 'sub':
                data = sub(
                  inputVal.val?.target?.value,
                  getFieldsValue()[changeOptions[field]['keyName']],
                  4,
                );
                break;
              case 'mul':
                data = mul(
                  inputVal.val?.target?.value,
                  getFieldsValue()[changeOptions[field]['keyName']],
                  4,
                );
                break;
              case 'div':
                data = div(
                  inputVal.val?.target?.value,
                  getFieldsValue()[changeOptions[field]['keyName']],
                  4,
                );
                break;
            }
          } else {
            data = `${inputVal.val?.target?.value}-${getFieldsValue()[changeOptions[field]['keyName']]}`;
          }

          setFieldsValue({
            [field]: data,
          });
        }
      }
    } else {
      // 如果是有选项的，也就是有options的
      if (ifHaveOptions) {
        updateSchema({
          field,
          ...changeOptions[field][inputVal.val?.target?.value || inputVal.val],
        });
      } else {
        // 如果change事件有输入值，
        if (getFieldsValue()[schemaItem.field]) {
          updateSchema([
            {
              field,
              ...changeOptions[field],
            },
            {
              field: schemaItem.field,
              required: true,
            },
          ]);
        } else {
          // 如果自己没有输入值，则判断组内其他是否有输入值
          if (getFieldsValue()[field]) {
            updateSchema([
              {
                field,
                required: true,
              },
              {
                field: schemaItem.field,
                ...changeOptions[field],
              },
            ]);
          } else {
            count++;
          }
        }

        // 如果全部都没有输入了，则复原，全部设置required为true
        if (count == keys(changeOptions).length) {
          keys(changeOptions).forEach((fieldItem) => {
            updateSchema({
              field: fieldItem,
              required: true,
            });
          });
        }
      }
    }
  });
};
