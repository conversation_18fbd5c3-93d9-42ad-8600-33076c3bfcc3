import { v4 as uuidv4 } from 'uuid';

/**
 * 获取UUID
 * @returns {*}
 */
export const getUUID = () => {
  return uuidv4();
};

/**
 * 复制对象(深拷贝)
 * @param source
 */
export const copy = (source) => {
  if (source) {
    const str = JSON.stringify(source);
    return JSON.parse(str);
  }
  return null;
};

/**
 * 判断审批类型
 * @param source
 */
export const typeFn = (type, approver) => {
  if (type == 1 && approver) {
    return '指定人员';
  } else if (type == 2) {
    return '部门主管';
  } else if (type == 3) {
    return '自由选择';
  } else if (type == 4) {
    return '多级部门负责人';
  } else {
    return '-';
  }
};
