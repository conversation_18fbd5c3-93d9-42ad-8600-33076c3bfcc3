<template>
  <!-- 审批人节点 -->
  <div>
    审批对象：
    {{ typeFn(node.config.type, props.node.config.approver) }}
  </div>
</template>

<script setup name="ApproverNode">
  import { getCurrentInstance, inject } from 'vue';
  import { KEY_VALIDATOR, KEY_PROCESS_DATA } from '../../config/keys';
  import { typeFn } from '../../utils/tools';

  const props = defineProps({
    tempNodeId: {
      // 临时节点ID
      type: String,
    },
    node: {
      // 传入的流程配置数据
      type: Object,
      default: {},
    },
  });

  const { proxy } = getCurrentInstance();

  // 获取流程数据
  const processData = inject(KEY_PROCESS_DATA);
  // 获取流程验证器实例
  const validator = inject(KEY_VALIDATOR);

  // 注册验证器
  validator.register(props.tempNodeId, () => {
    return {
      valid:
        (props.node.config.type == 1 && props.node.config.approver) || props.node.config.type !== 1,
      message: '请选择审批对象！',
    };
  });
</script>

<style lang="less" scoped></style>
