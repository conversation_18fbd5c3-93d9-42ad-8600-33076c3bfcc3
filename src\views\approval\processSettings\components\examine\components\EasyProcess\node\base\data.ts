import { isUndefined } from 'lodash-es';
import { FormSchema } from '@/components/Form';
import { getTreeWithDept } from '@/api/management/account';
import { getDeptTree } from '@/api/management/dept';
import { isPlainObject } from 'xe-utils';

/** 配置审批人schema */
export function approverSchemaFn(show?, title?): FormSchema[] {
  return [
    {
      field: 'title',
      label: '节点标题',
      component: 'Input',
      required: true,
      colProps: { span: 24 },
      defaultValue: title,
    },
    {
      field: 'type',
      label: '选择审批对象',
      component: 'RadioGroup',
      componentProps: ({ formActionType }) => {
        return {
          options: [
            {
              label: '指定人员',
              value: 1,
            },
            {
              label: '部门主管',
              value: 2,
            },
            {
              label: '自由选择',
              value: 3,
            },
            {
              label: '连续多级部门负责人',
              value: 4,
            },
          ],
          onChange: async (value) => {
            if (formActionType) {
              let { resetSchema, setFieldsValue, updateSchema } = formActionType;
              await setFieldsValue({ approver: undefined, approverText: undefined });
              let newValue = isPlainObject(value) ? value.target.value : value;

              let isMultiDept = newValue == 4;
              if (newValue == 2 || newValue == 3 || newValue == 4) {
                await resetSchema(approverSchemaFn(false));
              } else {
                await resetSchema(approverSchemaFn(true));
              }
              // 更新multiDeptLevel和approveMethod字段的显示状态
              await updateSchema([
                {
                  field: 'multiDeptLevel',
                  dynamicRules: () => [{ required: isMultiDept, message: '请选择多级部门终点' }],
                  ifShow: isMultiDept,
                },
                {
                  field: 'approveMethod',
                  ifShow: !isMultiDept,
                },
              ]);
            }
          },
        };
      },
      defaultValue: 1,
      colProps: { span: 24 },
    },
    {
      field: 'approver',
      label: '审批人',
      // component: 'ApiSelect',
      component: 'ApiTreeSelect',
      componentProps: ({ formActionType }) => {
        return {
          api: getTreeWithDept,
          params: {
            basicDeptId: 1,
            status: 1,
          },
          treeSelectProps: {
            mode: 'tags',
            maxTagCount: 50,
            treeDefaultExpandAll: true,
            showSearch: true,
            multiple: true,
            treeCheckable: true,
            treeLine: {
              showLeafIcon: false,
            },
            fieldNames: { children: 'children', key: 'name', value: 'id', label: 'name' },
            placeholder: '请选择用戶',
            allowClear: true,
            filterTreeNode: (search, item) => {
              if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
              return false;
            },
            onChange: async (key, value) => {
              if (formActionType) {
                let { setFieldsValue } = formActionType;
                let data = {};
                (key as string[]).forEach((item, index) => {
                  if (!isUndefined(item)) {
                    data[item] = value[index];
                  }
                });
                await setFieldsValue({ approverText: data });
              }
            },
          },
        };
      },
      required: true,
      ifShow: show,
      itemProps: {
        validateTrigger: 'blur',
      },
      colProps: { span: 24 },
    },
    {
      field: 'approverText',
      label: '审批人名称',
      // component: 'ApiSelect',
      component: 'Input',
      colProps: { span: 24 },
      ifShow: false,
      defaultValue: {},
    },
    {
      field: 'multiDeptLevel',
      label: '指定终点',
      component: 'Select',
      ifShow: ({ model }) => model.type === 4, // 选择连续多级部门负责人时显示
      dynamicRules: ({ model }) => {
        // 选择连续多级部门负责人时必填
        if (model.type === 4) {
          return [{ required: true, message: '请选择多级部门终点' }];
        }
        return [];
      },
      componentProps: {
        options: [
          {
            label: '直接部门负责人',
            value: 1,
          },
          {
            label: '第二级部门负责人',
            value: 2,
          },
          {
            label: '第三级部门负责人',
            value: 3,
          },
          {
            label: '第四级部门负责人',
            value: 4,
          },
          {
            label: '第五级部门负责人',
            value: 5,
          },
          {
            label: '第六级部门负责人',
            value: 6,
          },
          {
            label: '第七级部门负责人',
            value: 7,
          },
          {
            label: '第八级部门负责人',
            value: 8,
          },
          {
            label: '第九级部门负责人',
            value: 9,
          },
          {
            label: '第十级部门负责人',
            value: 10,
          },
        ],
      },
      defaultValue: 1,
      colProps: { span: 24 },
    },
    {
      field: 'allow_btn_text',
      label: '流程设置',
      component: 'Input',
      colProps: { span: 24 },
      defaultValue: '同意',
      required: true,
    },
    {
      field: 'notice',
      label: '通知',
      component: 'CheckboxGroup',
      colProps: { span: 24 },
      required: true,
      componentProps: {
        options: [
          { label: '企微通知', value: 1 },
          { label: '站内通知', value: 2 },
          { label: '邮箱通知', value: 3 },
        ],
      },
      defaultValue: [1, 2],
    },
    {
      field: 'approveMethod',
      label: '审批方式',
      component: 'RadioButtonGroup',
      helpMessage:
        '会签：三人会收到审批，需全部同意之后，审批才可到下一审批节点；或签：三人会收到审批，只要其中任意一人审批即可到下一审批节点。',
      componentProps: {
        options: [
          {
            label: '会签',
            value: 0,
          },
          {
            label: '或签',
            value: 1,
          },
        ],
      },
      defaultValue: 0,
      colProps: { span: 24 },
      ifShow: ({ model }) => model.type !== 4, // 当选择连续多级部门负责人时隐藏
    },
    {
      field: 'isReject',
      label: '允许驳回',
      component: 'RadioButtonGroup',
      componentProps: {
        options: [
          {
            label: '否',
            value: 0,
          },
          {
            label: '是',
            value: 1,
          },
        ],
      },
      defaultValue: 0,
      colProps: { span: 24 },
    },
    {
      field: 'isContinuous',
      label: '允许连审',
      component: 'RadioButtonGroup',
      componentProps: {
        options: [
          {
            label: '否',
            value: 0,
          },
          {
            label: '是',
            value: 1,
          },
        ],
      },
      defaultValue: 0,
      colProps: { span: 24 },
    },
    {
      field: 'keys',
      label: '表单外键',
      component: 'Input',
      colProps: { span: 24 },
    },
    {
      field: 'timedMode',
      label: '限时模式',
      component: 'Select',
      componentProps: {
        options: [
          {
            label: '不限时',
            value: 0,
          },
          {
            label: '1个小时内',
            value: 1,
          },
          {
            label: '3个小时内',
            value: 3,
          },
          {
            label: '5个小时内',
            value: 5,
          },
          {
            label: '10个小时内',
            value: 10,
          },
          {
            label: '24个小时内',
            value: 24,
          },
          {
            label: '72个小时内',
            value: 72,
          },
        ],
      },
      defaultValue: 0,
      colProps: { span: 24 },
    },
    {
      field: 'signer',
      label: '签阅人',
      component: 'ApiTreeSelect',
      componentProps: ({ formActionType }) => {
        return {
          api: getTreeWithDept,
          params: {
            basicDeptId: 1,
            status: 1,
          },
          treeSelectProps: {
            mode: 'tags',
            maxTagCount: 50,
            treeDefaultExpandAll: true,
            showSearch: true,
            multiple: true,
            treeCheckable: true,
            treeLine: {
              showLeafIcon: false,
            },
            fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
            placeholder: '请选择用戶',
            allowClear: true,
            filterTreeNode: (search, item) => {
              if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
              return false;
            },
            onChange: async (key, value) => {
              if (formActionType) {
                let { setFieldsValue } = formActionType;
                let data = {};
                (key as string[]).forEach((item, index) => {
                  if (!isUndefined(item)) {
                    data[item] = value[index];
                  }
                });
                await setFieldsValue({ signerText: data });
              }
            },
          },
        };
      },
      itemProps: {
        validateTrigger: 'blur',
      },
      colProps: { span: 24 },
    },
    {
      field: 'signerText',
      label: '签阅人名称',
      component: 'Input',
      colProps: { span: 24 },
      ifShow: false,
      defaultValue: {},
    },
    {
      field: 'integral',
      label: '获得积分',
      component: 'InputNumber',
      colProps: { span: 24 },
      ifShow: true,
      defaultValue: 0,
    },
  ];
}

/** 配置条件schema */
export function conditionSchemaFn(title): FormSchema[] {
  return [
    {
      field: 'title',
      label: '节点标题',
      component: 'Input',
      required: true,
      colProps: { span: 24 },
      defaultValue: title,
    },

    {
      field: 'personType',
      label: '人员类型',
      component: 'RadioGroup',
      colProps: { span: 24 },
      required: false,
      componentProps: {
        options: [
          { label: '无', value: 0 },
          { label: '申请人', value: 1 },
          { label: '直属上级', value: 2 },
        ],
      },
      defaultValue: 1,
    },

    {
      field: 'person',
      label: '成员',
      component: 'ApiTreeSelect',
      componentProps: ({ formActionType }) => {
        return {
          api: getTreeWithDept,
          params: {
            basicDeptId: 1,
            status: 1,
          },
          treeSelectProps: {
            mode: 'tags',
            maxTagCount: 50,
            treeDefaultExpandAll: true,
            showSearch: true,
            multiple: true,
            treeCheckable: true,
            treeLine: {
              showLeafIcon: false,
            },
            fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
            placeholder: '请选择用戶',
            allowClear: true,
            filterTreeNode: (search, item) => {
              if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
              return false;
            },
            onChange: async (key, value) => {
              if (formActionType) {
                let { setFieldsValue } = formActionType;
                let data = {};
                (key as string[]).forEach((item, index) => {
                  if (!isUndefined(item)) {
                    data[item] = value[index];
                  }
                });
                await setFieldsValue({ signerText: data });
              }
            },
          },
        };
      },
      itemProps: {
        validateTrigger: 'blur',
      },
      required: false,
      colProps: { span: 24 },
    },
    {
      field: 'deptType',
      label: '部门类型',
      component: 'RadioGroup',
      colProps: { span: 24 },
      required: false,
      componentProps: {
        options: [
          { label: '无', value: 0 },
          { label: '申请部门', value: 1 },
          { label: '上级部门', value: 2 },
        ],
      },
      defaultValue: 1,
    },
    {
      field: 'dept',
      label: '部门',
      component: 'ApiTreeSelect',
      componentProps: ({ formActionType }) => {
        return {
          api: getDeptTree,
          params: {
            all: 1,
            // status: 1,
          },
          treeSelectProps: {
            mode: 'tags',
            maxTagCount: 50,
            treeDefaultExpandAll: true,
            showSearch: true,
            multiple: true,
            treeCheckable: true,
            treeLine: {
              showLeafIcon: false,
            },
            fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
            placeholder: '请选择部门',
            allowClear: true,
            filterTreeNode: (search, item) => {
              if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
              return false;
            },
            onChange: async (key, value) => {
              if (formActionType) {
                let { setFieldsValue } = formActionType;
                let data = {};
                (key as string[]).forEach((item, index) => {
                  if (!isUndefined(item)) {
                    data[item] = value[index];
                  }
                });
                await setFieldsValue({ signerText: data });
              }
            },
          },
        };
      },
      itemProps: {
        validateTrigger: 'blur',
      },
      required: false,
      colProps: { span: 24 },
    },
  ];
}
