<template>
  <div>
    <BasicDrawer @register="registerDrawer" v-bind="$attrs" showFooter width="50%" @ok="handleOk">
      <BasicForm @register="registerForm" />
    </BasicDrawer>
  </div>
</template>

<script lang="ts" setup>
  import { formSchema } from '../datas/data';

  import { BasicForm, useForm } from '@/components/Form';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { message } from 'ant-design-vue';
  import { createSetting, updateSetting } from '@/api/approval/approval';
  import { ref } from 'vue';

  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(false);
  const recordData: any = ref(null);

  /** 注册 Form */
  const [registerForm, { resetFields, setFieldsValue, validate, resetSchema }] = useForm({
    baseColProps: { span: 12 },
    showActionButtonGroup: false,
    labelCol: { span: 7 },
  });

  /** 注册抽屉，刚进来会触发 */
  const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
    console.log(data);
    resetFields();
    isUpdate.value = data.isUpdate;
    recordData.value = data.record;
    await resetSchema(formSchema);

    if (isUpdate.value) {
      setFieldsValue(data.record);
    }
  });

  /** 提交 */
  const handleOk = async () => {
    try {
      let data = await validate();

      if (isUpdate.value) {
        await updateSetting({ ...data, id: recordData.value.id });
        message.success('编辑成功!');
      } else {
        await createSetting(data);
        message.success('提交成功!');
      }
      emit('success');
      closeDrawer();
    } catch (error) {
      console.log(error);
      message.error('操作失败!');
    }
  };
</script>

<style lang="less" scoped>
  :deep(.ant-picker) {
    width: 100%;
  }
  :deep(.ant-input-number-affix-wrapper) {
    width: 100%;
  }
</style>
