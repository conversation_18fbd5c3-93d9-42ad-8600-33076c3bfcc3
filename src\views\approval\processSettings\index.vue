<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleCreate">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <ApprovalDrawer @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { Button, message } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import type { ActionItem, EditRecordRow } from '@/components/Table';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useRouter } from 'vue-router';
  import { tableColumnsFn, searchSchema } from './datas/data';
  import ApprovalDrawer from './components/ApprovalDrawer.vue';
  import { useDrawer } from '@/components/Drawer';
  import { getList, deleteSetting } from '@/api/approval/approval';
  import { useApprovalStore } from '@/store/modules/approval';
  // import { usePermission } from '@/hooks/web/usePermission';

  const approvalStore = useApprovalStore();
  const router = useRouter();
  // const { hasPermission } = usePermission();

  /** 注册 创建、编辑 Drawer */
  const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer();

  /** 注册表格 */
  const [registerTable, { reload }] = useTable({
    title: '流程配置列表',
    showIndexColumn: false,
    api: getList,
    rowKey: 'id',
    dataSource: [{ strid: 123123, created_at: 123123, status: 0 }],
    columns: tableColumnsFn(handleSuccess),
    actionColumn: {
      width: 300,
      title: '操作',
      dataIndex: 'action',
    },
    immediate: true,
    showTableSetting: true,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
      labelWidth: 120,
      actionColOptions: {
        span: 24,
      },
      baseColProps: {
        span: 6,
      },
    },
  });

  /** 操作按钮 */
  function createActions(record: EditRecordRow): Recordable[] {
    let buttonList: ActionItem[] = [
      {
        icon: 'clarity:note-edit-line',
        label: '编辑',
        onClick: handleUpdate.bind(null, record),
        ifShow: true,
      },
      {
        icon: 'ant-design:branches-outlined',
        label: '设计',
        onClick: handleDesign.bind(null, record),
        ifShow: true,
      },
      {
        icon: 'ant-design:delete-outlined',
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record),
        },
        ifShow: true,
      },
    ];
    return buttonList;
  }

  /** 新增 */
  const handleCreate = () => {
    openDrawer(true, { isUpdate: false });
    setDrawerProps({
      title: '新增流程配置',
    });
  };

  /** 编辑 */
  const handleUpdate = (record) => {
    openDrawer(true, { isUpdate: true, record });
    setDrawerProps({
      title: '编辑流程配置',
    });
  };

  /** 设计 */
  const handleDesign = (record) => {
    approvalStore.setRecordData(record);
    router.push('/approval/configuration');
  };

  /** 删除 */
  async function handleDelete(record) {
    try {
      await deleteSetting({ id: record.id });
      message.success('删除成功!');
      reload();
    } catch (error) {
      console.log(error);
      message.success('删除失败!');
    }
  }

  /**  回调 */
  function handleSuccess() {
    reload();
  }
</script>

<style lang="less" scoped></style>
