<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <HTMLCodeStyleSettings>
      <option name="HTML_TEXT_WRAP" value="0" />
      <option name="HTML_ALIGN_ATTRIBUTES" value="false" />
      <option name="HTML_SPACE_INSIDE_EMPTY_TAG" value="true" />
      <option name="HTML_DO_NOT_INDENT_CHILDREN_OF" value="html,body,thead,tbody,tfoot,script,style" />
      <option name="HTML_ENFORCE_QUOTES" value="true" />
    </HTMLCodeStyleSettings>
    <JSCodeStyleSettings version="0">
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="ALIGN_VAR_STATEMENTS" value="1" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="REFORMAT_C_STYLE_COMMENTS" value="true" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="Remove" />
      <option name="VAR_DECLARATION_WRAP" value="0" />
      <option name="IMPORTS_WRAP" value="2" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
      <option name="INDENT_CHAINED_CALLS" value="false" />
      <option name="SPACES_WITHIN_INTERPOLATION_EXPRESSIONS" value="true" />
    </JSCodeStyleSettings>
    <TypeScriptCodeStyleSettings version="0">
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="ALIGN_VAR_STATEMENTS" value="1" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="REFORMAT_C_STYLE_COMMENTS" value="true" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="Remove" />
      <option name="IMPORTS_WRAP" value="0" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
      <option name="SPACES_WITHIN_INTERPOLATION_EXPRESSIONS" value="true" />
    </TypeScriptCodeStyleSettings>
    <VueCodeStyleSettings>
      <option name="INDENT_CHILDREN_OF_TOP_LEVEL" value=", template" />
      <option name="INTERPOLATION_NEW_LINE_AFTER_START_DELIMITER" value="false" />
      <option name="INTERPOLATION_NEW_LINE_BEFORE_END_DELIMITER" value="false" />
    </VueCodeStyleSettings>
    <codeStyleSettings language="CSS">
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="HTML">
      <option name="SOFT_MARGINS" value="80" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
        <option name="SMART_TABS" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JSON">
      <indentOptions>
        <option name="INDENT_SIZE" value="4" />
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <option name="ALIGN_MULTILINE_CHAINED_METHODS" value="true" />
      <option name="SOFT_MARGINS" value="80" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="LESS">
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="SASS">
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="SCSS">
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="TypeScript">
      <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="SPACE_BEFORE_METHOD_PARENTHESES" value="true" />
      <option name="SOFT_MARGINS" value="80" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="Vue">
      <option name="SOFT_MARGINS" value="80" />
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>