import { defineStore } from 'pinia';
import { store } from '@/store';

export const useApprovalStore = defineStore('approval', {
  state: () => ({
    recordData: null,
  }),
  getters: {
    getRecordData: (state) => {
      return state.recordData;
    },
  },
  actions: {
    setRecordData(record) {
      this.recordData = record;
    },
  },
});

export function useLocaleStoreWithOut() {
  return useApprovalStore(store);
}
