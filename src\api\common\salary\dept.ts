import { defHttp } from '@/utils/http/axios';

enum Api {
  listUrl = '/salary/dept/getPageList',
  createUrl = '/salary/dept/create',
  deleteUrl = '/salary/dept/delete',
  changeUrl = '/salary/dept/change',
  DetailUrl = '/salary/dept/detail',
}
export const listAction = (params?: { tabIndex: Number; page: Number; pageSize: Number }) =>
  defHttp.get({ url: Api.listUrl, params });

export const createAction = (params?) => defHttp.post({ url: Api.createUrl, params });

export const deleteAction = (params: { _id: Number; _key: string; _subid: Number }) =>
  defHttp.post({ url: Api.deleteUrl, params });

export const changeAction = (params) => defHttp.post({ url: Api.changeUrl, params });

export const detailAction = (params: { id: number }) => defHttp.get({ url: Api.DetailUrl, params });
