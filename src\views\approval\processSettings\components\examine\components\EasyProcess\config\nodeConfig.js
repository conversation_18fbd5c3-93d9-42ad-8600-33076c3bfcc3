import { ROUTER, CONDITION, START, APPROVER, NOTIFY, ENDPERSONS } from './nodeType'
import { TeamOutlined, PartitionOutlined, SendOutlined, HeatMapOutlined, SlidersOutlined } from '@ant-design/icons-vue'
// 节点配置
export const nodeConfig = {}

// 发起人节点配置
nodeConfig[START] = {
  title: '发起', // 节点标题
  color: '#FFFFFF', // 节点标题颜色
  bgColor: '#1e83e9', // 节点标题背景颜色
  canAdd: false, // 节点是否可以增加
  canRemoved: false, // 节点是否能够移除
  hasDrawer: true, // 节点是否可以进行配置
  // "hasAddBtn":true, //是否有添加节点按钮,
  icon: {
    // 图标
    name: 'start', // 图标名
    color: '#1e83e9' // 颜色
  },
  defaultNode: {
    // 默认节点结构，用于添加节点时
    nodeType: 'start',
    config: {
      title: '发起人'
    },
    childNode: null
  }
}

// 路由节点配置
nodeConfig[ROUTER] = {
  title: '条件分支', // 节点标题
  canAdd: true, // 节点是否可以增加
  hasDrawer: true, // 节点是否可以进行配置
  // "hasAddBtn":true, //是否有添加节点按钮
  icon: {
    // 图标
    name: 'router', // 图标名
    color: '#3CB371', // 颜色
    icon: PartitionOutlined
  },
  defaultNode: {
    // 默认节点结构，用于添加节点时
    nodeName: '路由',
    nodeType: 'router',
    config: {},
    childNode: null,
    conditionNodes: [
      {
        nodeType: 'condition',
        isLastCondition: false,
        config: {
          title: '条件'
        },
        childNode: null
      },
      {
        nodeType: 'condition',
        isLastCondition: false,
        config: {
          title: '条件'
        },
        childNode: null
      }
    ]
  }
}

// 条件节点配置
nodeConfig[CONDITION] = {
  title: '条件', // 节点标题
  color: '#FFFFFF', // 节点标题颜色
  bgColor: '#3CB371', // 节点标题背景颜色
  canAdd: false, // 节点是否可以增加
  canRemoved: true, // 节点是否能够移除
  hasDrawer: true, // 节点是否可以进行配置
  // "hasAddBtn":true, //是否有添加节点按钮
  icon: {
    // 图标
    name: 'condition', // 图标名
    color: '#3CB371', // 颜色
    icon: SlidersOutlined
  },
  defaultNode: {
    nodeType: 'condition',
    isLastCondition: false,
    config: {
      title: '条件'
    },
    childNode: {}
  }
}

// 审核人节点配置
nodeConfig[APPROVER] = {
  title: '审批', // 节点标题
  color: '#FFFFFF', // 节点标题颜色
  bgColor: '#FF8C00', // 节点标题背景颜色
  canAdd: true, // 节点是否可以增加
  canRemoved: true, // 节点是否能够移除
  hasDrawer: true, // 节点是否可以进行配置
  // "hasAddBtn":true, //是否有添加节点按钮
  icon: {
    // 图标
    name: 'approver', // 图标名
    color: '#FF8C00', // 颜色
    icon: TeamOutlined
  },
  defaultNode: {
    // 默认节点结构，用于添加节点时
    nodeName: '审批',
    nodeType: 'approver',
    config: {
      title: '审批',
      type: 1
    },
    childNode: null
  }
}

// 抄送人节点配置
nodeConfig[NOTIFY] = {
  title: '抄送', // 节点标题
  color: '#FFFFFF', // 节点标题颜色
  bgColor: '#808000', // 节点标题背景颜色
  canAdd: false, // 节点是否可以增加
  canRemoved: true, // 节点是否能够移除
  hasDrawer: true, // 节点是否可以进行配置
  // "hasAddBtn":true, //是否有添加节点按钮
  icon: {
    // 图标
    name: 'notify', // 图标名
    color: '#808000', // 颜色
    icon: SendOutlined // 自定义图标
  },
  defaultNode: {
    // 默认节点结构，用于添加节点时
    nodeName: '抄送',
    nodeType: 'notify',
    config: {
      title: '抄送'
    },
    childNode: null
  }
}

// 结束人节点配置
nodeConfig[ENDPERSONS] = {
  title: '结束', // 节点标题
  color: '#FFFFFF', // 节点标题颜色
  bgColor: 'red', // 节点标题背景颜色
  canAdd: true, // 节点是否可以增加
  canRemoved: true, // 节点是否能够移除
  hasDrawer: true, // 节点是否可以进行配置
  // "hasAddBtn":false, //是否有添加节点按钮
  icon: {
    // 图标
    name: 'endpersons', // 图标名
    color: 'red', // 颜色
    icon: HeatMapOutlined
  },
  defaultNode: {
    // 默认节点结构，用于添加节点时
    nodeName: '结束',
    nodeType: 'endpersons',
    config: {
      title: '结束',
      type: 1
    },
    childNode: null
  }
}
