<template>
  <!-- 发起人节点 -->
  <div>
    审批对象：
    {{ typeFn(node.config.type, props.node.config.approver) }}
  </div>
</template>

<script setup name="StartNode" lang="ts">
import { inject } from 'vue'
import { KEY_VALIDATOR } from '../../config/keys'
import { typeFn } from '../../utils/tools'
import { keys } from 'lodash-es'

const props = defineProps({
  tempNodeId: {
    // 临时节点ID
    type: String
  },
  node: {
    // 传入的流程配置数据
    type: Object,
    default: {}
  }
})

// const { proxy } = getCurrentInstance()

// // 获取流程数据
// const processData = inject(KEY_PROCESS_DATA)
// 获取流程验证器实例
const validator: any = inject(KEY_VALIDATOR)

// 注册验证器
validator.register(props.tempNodeId, () => {
  console.log(props.node)

  return {
    valid: keys(props.node.config).length !== 0,
    message: '请选择发起人'
  }
})
</script>

<style lang="less" scoped></style>
