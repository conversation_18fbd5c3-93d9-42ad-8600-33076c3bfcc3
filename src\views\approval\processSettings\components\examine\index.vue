<template>
  <div class="app-container">
    <!-- <div class="header">
      <div class="title">{{ processData.processName }}</div>
      <div class="publish">
        <el-button type="primary" @click="isShowInfo = true" round plain>项目信息</el-button>
        
      </div>
    </div> -->
    <div class="header">
      <span style="font-weight: bold">流程名称：{{ approvalStore.getRecordData?.name }}</span>
      <div class="publish">
        <Button @click="submit">提交</Button>
      </div>
    </div>
    <div class="body">
      <ProcessDesigner ref="process" :data="processData" />
    </div>
  </div>
</template>

<script setup name="WorkFlow" lang="ts">
  import ProcessDesigner from './components/EasyProcess/ProcessDesigner.vue';
  // import { ElMessageBox } from 'element-plus'
  import { getCurrentInstance, onMounted, ref } from 'vue';
  import { Button, message } from 'ant-design-vue';
  import { useApprovalStore } from '@/store/modules/approval';
  import { cloneDeep } from 'lodash-es';
  import { updateSetting } from '@/api/approval/approval';
  import { useRouter } from 'vue-router';

  const approvalStore: any = useApprovalStore();
  const router = useRouter();

  const { proxy } = getCurrentInstance();
  // import mockData from '~/public/mock/data.json'

  // 审批数据
  let processData: any = ref(null);
  // let showDrawer = ref(true)
  // let isShowInfo = ref(false)

  onMounted(async () => {
    processData.value = cloneDeep(approvalStore.getRecordData.form_data);
    console.log(processData.value);
    // 获取审批数据
    // processData.value = {
    //   processId: '10001',
    //   nodeConfig: {
    //     nodeType: 'start',
    //     config: {
    //       name: null
    //     }
    //   }
    // }
  });

  /** 提交 */
  const submit = () => {
    try {
      proxy.$refs.process.validate(async (valid) => {
        if (valid) {
          let result = proxy.$refs.process.getResult();
          console.info(result);
          await updateSetting({ ...approvalStore.getRecordData, form_data: result });
          message.success('提交成功！');
          router.push({
            path: '/approval/processSettings',
          });
        }
      });
    } catch (error) {
      console.log(error);
      message.error('提交失败！');
    }
  };
</script>

<style lang="less" scoped>
  .app-container {
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: auto;
    height: 100%;
  }
  .header {
    height: 50px;
    background-color: #1c84c6;
    display: flex;
    align-items: center;
    padding: 0px 16px;
    justify-content: space-between;
    z-index: 99;

    .title {
      color: #ffffff;
      font-weight: bold;
    }
  }
  .body {
    position: absolute;
    padding-top: 50px;
    //width: 100%;
    min-width: 100%;
    height: 100%;
    overflow: scroll;
  }
</style>
