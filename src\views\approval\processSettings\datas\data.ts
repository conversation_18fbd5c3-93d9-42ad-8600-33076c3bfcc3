import { Switch } from 'ant-design-vue';
import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { updateSetting } from '@/api/approval/approval';
import { debounce } from 'lodash-es';
// import { getChildSystem } from '/@/api/approval/approval'
// import { getSiteSelectList } from '@/api/management/site';

/** 筛选 */
export const searchSchema: FormSchema[] = [
  {
    field: 'name',
    label: '流程名称',
    component: 'Input',
  },
  // {
  //   field: 'type',
  //   label: '流程分类',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '内置', value: 1 },
  //       { label: '外置', value: 2 },
  //     ],
  //   },
  // },
  // {
  //   field: 'system',
  //   label: '所属系统',
  //   component: 'PagingApiSelect',
  //   componentProps: () => {
  //     return {
  //       api: getSiteSelectList,
  //       searchMode: true,
  //       pagingMode: true,
  //       selectProps: {
  //         fieldNames: { value: 'name', label: 'name' },
  //         optionFilterProp: 'name',
  //         showSearch: true,
  //         placeholder: '请选择',
  //         allowClear: true,
  //       },
  //       resultField: 'items',
  //     };
  //   },
  //   itemProps: {
  //     validateTrigger: 'blur',
  //   },
  // },
  // {
  //   field: 'desc',
  //   label: '流程说明',
  //   component: 'InputTextArea',
  // },
];

/** 表格 */
export const tableColumnsFn = (reload: Function): BasicColumn[] => {
  return [
    {
      title: '流程名称',
      dataIndex: 'name',
      width: 200,
      resizable: true,
    },
    {
      title: '流程图标',
      dataIndex: 'icon',
      width: 200,
      resizable: true,
    },
    {
      title: '图标颜色',
      dataIndex: 'color',
      width: 200,
      resizable: true,
    },
    {
      title: '流程分类',
      dataIndex: 'type',
      width: 200,
      resizable: true,
    },
    {
      title: '所属系统',
      dataIndex: 'system',
      width: 200,
      resizable: true,
    },
    {
      title: '小程序地址',
      dataIndex: 'uniapp_url',
      width: 200,
      resizable: true,
    },
    {
      title: 'PC地址',
      dataIndex: 'pc_url',
      width: 200,
      resizable: true,
    },
    {
      title: '排序',
      dataIndex: 'sort',
      width: 200,
      resizable: true,
    },
    {
      title: '流程说明',
      dataIndex: 'note',
      width: 200,
      resizable: true,
      customRender: ({ text }) => {
        return text || text == '' ? '-' : text;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 200,
      resizable: true,
      customRender: ({ record, text }) => {
        return h(Switch, {
          checked: text === 1,
          checkedChildren: '启用',
          unCheckedChildren: '停用',
          onChange: debounce(async (value) => {
            const { createMessage } = useMessage();
            const newStatus = value ? 1 : 0;
            try {
              await updateSetting({ ...(record as any), status: newStatus });
              createMessage.success(`修改状态成功!`);
              reload();
            } catch (error) {
              createMessage.success(`修改状态失败!`);
              console.log(error);
            }
          }, 300),
        });
      },
    },
  ];
};

/** 新增 */
export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '流程名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'icon',
    label: '流程图标',
    component: 'Input',
    required: true,
  },
  {
    field: 'color',
    label: '图标颜色',
    component: 'Input',
    required: true,
  },
  {
    field: 'type',
    label: '流程分类',
    component: 'Select',
    componentProps: {
      options: [
        { label: '内置', value: 1 },
        { label: '外置', value: 2 },
      ],
    },
    defaultValue: 2,
  },
  // {
  //   field: 'system',
  //   label: '所属系统',
  //   component: 'PagingApiSelect',
  //   componentProps: () => {
  //     return {
  //       api: getSiteSelectList,
  //       searchMode: true,
  //       pagingMode: true,
  //       selectProps: {
  //         fieldNames: { value: 'name', label: 'name' },
  //         optionFilterProp: 'name',
  //         showSearch: true,
  //         placeholder: '请选择',
  //         allowClear: true,
  //       },
  //       resultField: 'items',
  //     };
  //   },
  //   itemProps: {
  //     validateTrigger: 'blur',
  //   },
  //   required: true,
  // },
  {
    field: 'uniapp_url',
    label: '小程序地址',
    component: 'Input',
  },
  {
    field: 'pc_url',
    label: 'PC地址',
    component: 'Input',
  },
  {
    field: 'sort',
    label: '排序',
    component: 'InputNumber',
  },
  {
    field: 'entity',
    label: '外置表单类型',
    component: 'Input',
  },
  {
    field: 'entity_field',
    label: '外置表单字段',
    component: 'Input',
  },

  {
    field: 'desc',
    label: '流程说明',
    component: 'InputTextArea',
  },
];
