<template>
  <!-- 条件节点 -->
  <div class="ep-node-condition">
    <!-- <div v-if="!props.node.isLastCondition"> 条件: 这是死数据 </div>
    <div v-else> 其他条件进入此流程 </div> -->
    <!-- 人员类型：{{ props.node.config.personType ? (props.node.config.personType == 1 ? '申请人' : '审核人') : '-' }} -->
    条件 : {{ displayCondition }}
  </div>
</template>

<script setup name="ConditionNode">
  import { getCurrentInstance, inject, computed } from 'vue';
  import { KEY_PROCESS_DATA, KEY_VALIDATOR } from '../../config/keys';
  import { typeFn } from '../../utils/tools';

  const props = defineProps({
    tempNodeId: {
      // 临时节点ID
      type: String,
    },
    node: {
      // 传入的流程配置数据
      type: Object,
      default: {},
    },
  });

  const displayCondition = computed(() => {
    const deptType = props.node.config.deptType;
    const personType = props.node.config.personType;

    let deptText = '';
    let personText = '';

    if (deptType === 1) {
      deptText = '申请部门';
    } else if (deptType === 2) {
      deptText = '上级部门';
    }

    if (personType === 1) {
      personText = '申请人';
    } else if (personType === 2) {
      personText = '直属上级';
    }

    if (deptText && personText) {
      return `${deptText} 或 ${personText}`;
    } else {
      if (personText) {
        return personText;
      }
      if (deptText) {
        return deptText;
      }
    }
    return '无';
  });

  const { proxy } = getCurrentInstance();

  // 获取流程数据
  const processData = inject(KEY_PROCESS_DATA);
  // 获取流程验证器实例
  const validator = inject(KEY_VALIDATOR);

  // 注册验证器
  validator.register(props.tempNodeId, () => {
    let valid = true;
    if (!props.node.isLastCondition) {
      if (!props.node.config.title) {
        valid = false;
      }
    }
    return {
      valid: valid,
      message: '请选择条件',
    };
  });
</script>

<style lang="less" scoped></style>
