import { defHttp } from '@/utils/http/axios';

enum Api {
  GetList = '/process-set/list',
  CreateSetting = '/process-set/create',
  DeleteSetting = '/process-set/delete',
  UpdateSetting = '/process-set/update',
  GetChildSystem = '/process-set/getClientList',
}

/** 获取表格列表 */
export const getList = (params?) => defHttp.get({ url: Api.GetList, params });

/** 新增 */
export const createSetting = (params: {
  name: string;
  icon: string;
  color: string;
  type?: number;
  system: string;
  sort?: number;
  entity?: string;
  entity_field?: string;
  note?: string;
  form_data?: string;
  status?: number;
}) => defHttp.post({ url: Api.CreateSetting, params });

/** 删除 */
export const deleteSetting = (params: { id: Number }) =>
  defHttp.post({ url: Api.DeleteSetting, params });

/** 编辑 */
export const updateSetting = (params: {
  id: number;
  name: string;
  icon: string;
  color: string;
  type?: number;
  system: string;
  sort?: number;
  entity?: string;
  entity_field?: string;
  note?: string;
  form_data?: string;
  status?: number;
}) => defHttp.post({ url: Api.UpdateSetting, params });

/** 获取子系统 */
export const getChildSystem = (params?) => defHttp.get({ url: Api.GetList, params });
