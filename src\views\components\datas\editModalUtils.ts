import {
  getPersonList,
  getDeptList,
  getCustomList,
  getBusinessList,
  getProjectList,
  getCountriesList,
} from '@/api/common';
import { uploadApi } from '@/api/sys/upload';
import { optionsFn } from './common';
import { defHttp } from '@/utils/http/axios';
import { h, reactive, ref } from 'vue';
import { keys } from 'lodash-es';
import { MarkDown } from '@/components/Markdown';
import { Tinymce } from '@/components/Tinymce';
// import Quick from '@/views/components/crm/form/Quick.vue';
// import RadioColor from '@/views/components/crm/form/RadioColor.vue';
// import Linkage from '@/views/components/crm/form/Linkage.vue';
// import TableSlot from '@/views/components/crm/form/TableSlot.vue';
// import SearchTable from '@/views/components/crm/form/SearchTable.vue';
// import PiDetailsTable from '@/views/components/crm/form/PiDetailsTable.vue';
// import CustomTable from '@/views/components/crm/form/CustomTable.vue';
// import OverallTitle from '@/views/components/crm/form/OverallTitle.vue';

export const dynamictabConfig = ref({});
export const fileConfig = ref({});

// 存储apiselcetchange类型和pi产品明细表格的联动数据
let piDetailsConfig = reactive({
  piid: '',
  field: '',
  ifDraft: false,
});

/** 下拉类型
 * @params schemaItem 接口返回的配置信息
 */
export const setSelectSchemas = (schemaItem) => {
  return {
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: 'label',
      ...schemaItem,
      mode: schemaItem?.multiple ? 'multiple' : '', // 多选
      options: optionsFn(schemaItem.options),
    },
  };
};
export const setRadioSchemas = (schemaItem) => {
  return {
    component: 'RadioGroup',
    componentProps: {
      ...schemaItem,
      options: optionsFn(schemaItem.options),
    },
  };
};
export const setTextAreaSchemas = (schemaItem) => {
  return {
    component: 'InputTextArea',
    componentProps: {
      ...schemaItem,
    },
  };
};
export const setDateSchemas = (schemaItem) => {
  return {
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: { width: '100%' },
      ...schemaItem,
    },
  };
};
export const setTimeSchemas = (schemaItem) => {
  return {
    component: 'TimePicker',
    componentProps: {
      style: { width: '100%' },
      ...schemaItem,
    },
  };
};
export const setDateTimeSchemas = (schemaItem) => {
  return {
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      format: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
      style: { width: '100%' },
      ...schemaItem,
    },
  };
};
export const setRangePickerSchemas = (schemaItem) => {
  return {
    component: 'RangePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm',
      format: 'YYYY-MM-DD HH:mm',
      style: { width: '100%' },
      ...schemaItem,
    },
  };
};
export const setNumberSchemas = (schemaItem) => {
  return {
    component: 'InputNumber',
    componentProps: {
      min: 0,
      ...schemaItem,
    },
    dynamicRules: () => {
      return [
        {
          required: schemaItem?.required || false,
          validator: (_, value) => {
            if (schemaItem?.required) {
              if (schemaItem?.canItBeZero && value === 0) {
                return Promise.reject(`${schemaItem.title}不能为0!`);
              }

              if (!value && value !== 0) {
                return Promise.reject(`请输入${schemaItem.title}！`);
              }
            }

            return Promise.resolve();
          },
        },
      ];
    },
  };
};
export const setStridTextSchemas = () => {
  return {
    component: 'Input',
    dynamicRules: () => {
      return [
        {
          required: true,
          validator: (_, value) => {
            let flag = /^-?\d+(-\d+)?$/.test(String(value));
            if (!flag) {
              return Promise.reject('请输入正确的格式！');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  };
};
export const setUploadImageSchemas = () => {
  return {
    component: 'ImageUpload',
    componentProps: {
      resultField: 'data.url',
      api: (file, progress) => {
        return new Promise((resolve, reject) => {
          uploadApi(file, progress)
            .then((uploadApiResponse) => {
              // 获取上传后的图片 URL
              const url = [uploadApiResponse.data.result.path];
              resolve({
                code: 200,
                data: {
                  url, // 逐张添加 URL
                },
              });
            })
            .catch((error) => {
              reject(error);
            });
        });
      },
    },
  };
};
export const setPersonSchemas = (schemaItem) => {
  return {
    component: 'ApiTreeSelect',
    componentProps: {
      api: getPersonList,
      immediate: true,
      treeSelectProps: {
        mode: 'tags',
        maxTagCount: 50,
        treeDefaultExpandAll: true,
        showSearch: true,
        multiple: schemaItem?.multiple || false,
        treeLine: {
          showLeafIcon: false,
        },
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        allowClear: true,
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
          return false;
        },
      },
      ...schemaItem,
    },
  };
};
export const setDeptSchemas = (schemaItem, data) => {
  return {
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptList,
      immediate: true,
      treeSelectProps: {
        mode: 'tags',
        maxTagCount: 50,
        treeDefaultExpandAll: true,
        showSearch: true,
        multiple: schemaItem?.multiple || false,
        treeLine: {
          showLeafIcon: false,
        },
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        allowClear: true,
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
          return false;
        },
      },
      disabled:
        data?._type == 'detail' ||
        (data?._type == 'edit' && schemaItem?.isedit) ||
        schemaItem?.dynamicDisabled,
      placeholder: '请选择',
      ...schemaItem,
    },
  };
};
export const setCountriesSchemas = (schemaItem, data) => {
  return {
    component: 'ApiSelect',
    componentProps: {
      api: getCountriesList,
      fieldNames: { key: 'key', value: 'id', label: 'name' },
      searchPlaceholder: '请选择',
      showSearch: true,
      optionFilterProp: 'name',
      mode: schemaItem?.multiple ? 'multiple' : '',
      disabled:
        data?._type == 'detail' ||
        (data?._type == 'edit' && schemaItem?.isedit) ||
        schemaItem?.dynamicDisabled,
    },
    itemProps: {
      validateTrigger: 'blur',
    },
  };
};
export const setCustomSchemas = (schemaItem, data) => {
  return {
    component: 'PagingApiSelect',
    componentProps: ({ formActionType }) => {
      let { clearValidate } = formActionType;
      return {
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        api: getCustomList,
        resultField: 'result',
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          mode: schemaItem?.multiple ? 'multiple' : '',
          disabled:
            data?._type == 'detail' ||
            (data?._type == 'edit' && schemaItem?.isedit) ||
            schemaItem?.dynamicDisabled,
        },
        onChange: async () => {
          await clearValidate();
        },
        ...schemaItem,
      };
    },
    itemProps: {
      validateTrigger: 'blur',
    },
  };
};
export const setBusinessSchemas = (schemaItem, data) => {
  return {
    component: 'PagingApiSelect',
    componentProps: ({ formActionType }) => {
      let { clearValidate } = formActionType;
      return {
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        api: getBusinessList,
        resultField: 'result',
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          mode: schemaItem?.multiple ? 'multiple' : '',
          disabled:
            data?._type == 'detail' ||
            (data?._type == 'edit' && schemaItem?.isedit) ||
            schemaItem?.dynamicDisabled,
        },
        onChange: async () => {
          await clearValidate();
        },
        ...schemaItem,
      };
    },
    itemProps: {
      validateTrigger: 'blur',
    },
  };
};
export const setProjectSchemas = (schemaItem, data) => {
  return {
    component: 'PagingApiSelect',
    componentProps: ({ formActionType }) => {
      let { clearValidate } = formActionType;
      return {
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        api: getProjectList,
        resultField: 'result',
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          mode: schemaItem?.multiple ? 'multiple' : '',
          disabled:
            data?._type == 'detail' ||
            (data?._type == 'edit' && schemaItem?.isedit) ||
            schemaItem?.dynamicDisabled,
        },
        onChange: async () => {
          await clearValidate();
        },
        ...schemaItem,
      };
    },
    itemProps: {
      validateTrigger: 'blur',
    },
  };
};
export const setSelectApiSchemas = (schemaItem, data) => {
  return {
    component: 'PagingApiSelect',
    componentProps: ({ formActionType }) => {
      let { clearValidate } = formActionType;
      return {
        ...schemaItem,
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        api: (params?) => defHttp.get({ url: schemaItem.api, params }),
        resultField: schemaItem?.resultField ?? 'result',
        returnParamsField: schemaItem?.returnParamsField ?? 'id',
        selectProps: {
          fieldNames: schemaItem?.FieldConfig ?? { key: 'id', value: 'value', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: schemaItem?.optionFilterProp ?? 'name',
          allowClear: true,
          mode: schemaItem?.multiple ? 'multiple' : '',
          disabled:
            data?._type == 'detail' ||
            (data?._type == 'edit' && schemaItem?.isedit) ||
            schemaItem?.dynamicDisabled,
        },
        onChange: async () => {
          await clearValidate();
        },
      };
    },
    itemProps: {
      validateTrigger: 'blur',
    },
  };
};

export const setSelectApiChangeSchemas = (schemaItem, data) => {
  return {
    component: 'PagingApiSelect',
    componentProps: {
      ...schemaItem,
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      api: (params?) => defHttp.get({ url: schemaItem.api, params }),
      resultField: schemaItem?.resultField ?? 'result',
      returnParamsField: schemaItem?.returnParamsField ?? 'id',
      selectProps: {
        fieldNames: schemaItem?.FieldConfig ?? { key: 'id', value: 'value', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: schemaItem?.optionFilterProp ?? 'name',
        allowClear: true,
        mode: schemaItem?.multiple ? 'multiple' : '',
        disabled:
          data?._type == 'detail' ||
          (data?._type == 'edit' && schemaItem?.isedit) ||
          schemaItem?.dynamicDisabled,
        onChange: (val) => {
          // 如果有和pi产品明细的表格联动
          if (
            Reflect.has(schemaItem, 'change') &&
            keys(schemaItem.change.change_options).includes(piDetailsConfig['field'])
          ) {
            piDetailsConfig['piid'] = val;
            piDetailsConfig['ifDraft'] = true;
          }
        },
      },
    },
    itemProps: {
      validateTrigger: 'blur',
    },
  };
};
export const setTreeSelectApiSchemas = (schemaItem, data) => {
  return {
    component: 'ApiTreeSelect',
    componentProps: {
      ...schemaItem,
      api: (params?) => defHttp.get({ url: schemaItem.api, params }),
      treeSelectProps: {
        mode: 'tags',
        maxTagCount: 50,
        treeDefaultExpandAll: true,
        showSearch: true,
        multiple: schemaItem?.multiple || false,
        treeLine: {
          showLeafIcon: false,
        },
        disabled:
          data?._type == 'detail' ||
          (data?._type == 'edit' && schemaItem?.isedit) ||
          schemaItem?.dynamicDisabled,
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        allowClear: true,
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0;
          return false;
        },
      },
    },
  };
};
export const setTinymceSchemas = () => {
  return {
    component: 'Input',
    render: ({ model, field }) => {
      return h(Tinymce, {
        value: model[field],
        onChange: (value: string) => {
          model[field] = value;
        },
      });
    },
  };
};
export const setMarkDownSchemas = () => {
  return {
    component: 'Input',
    render: ({ model, field }) => {
      return h(MarkDown, {
        value: model[field],
        onChange: (value: string) => {
          model[field] = value;
        },
      });
    },
  };
};
export const setDownloadSchemas = () => {
  return {
    component: 'Input',
    render: ({ model, field }) => {
      let vNode = h(
        'span',
        null,
        model[field].map((item) => {
          return h(
            'a',
            { href: item.value, target: '_blank', style: { marginRight: '10px' } },
            item.label,
          );
        }),
      );
      return vNode;
    },
  };
};
export const setQuickSchemas = (schemaItem) => {
  let setFieldsValue;
  return {
    component: 'Input',
    componentProps: ({ formActionType }) => {
      setFieldsValue = formActionType.setFieldsValue;
    },
    render: ({ model, field }) => {
      return h(Quick, {
        config: {
          keyword: schemaItem?.keyword || [],
        },
        modelValue: model[field],
        field,
        modalFn: { setFieldsValue },
      });
    },
  };
};
export const setRadioColorSchemas = (schemaItem) => {
  let setFieldsValue;
  return {
    component: 'Input',
    componentProps: ({ formActionType }) => {
      setFieldsValue = formActionType.setFieldsValue;
    },
    render: ({ model, field }) => {
      return h(RadioColor, {
        config: {
          colorArr: schemaItem.color_arr,
        },
        modelValue: model[field],
        field,
        modalFn: { setFieldsValue },
      });
    },
  };
};
export const setLinkageSchemas = (schemaItem) => {
  let setFieldsValue;
  return {
    component: 'Input',
    componentProps: ({ formActionType }) => {
      setFieldsValue = formActionType.setFieldsValue;
    },
    render: ({ model, field }) => {
      return h(Linkage, {
        config: {
          options: schemaItem.linkage_options,
        },
        modelValue: model[field],
        field,
        modalFn: { setFieldsValue },
      });
    },
  };
};
export const setTableSlotSchemas = (schemaItem, data) => {
  let setFieldsValue;
  return {
    component: 'Input',
    componentProps: ({ formActionType }) => {
      setFieldsValue = formActionType.setFieldsValue;
    },
    render: ({ field }) => {
      return h(TableSlot, {
        dataSource: data.data[field],
        columns: schemaItem.cols,
        modelValue: data?.process?.form_value?._pi,
        config: schemaItem,
        api: data.data?.api,
        apiParams: data.data?.apiParams,
        field,
        modalFn: { setFieldsValue },
      });
    },
  };
};
export const setSearchTableSchemas = (schemaItem) => {
  let setFieldsValue;
  let getFieldsValue;
  return {
    component: 'Input',
    componentProps: ({ formActionType }) => {
      setFieldsValue = formActionType.setFieldsValue;
      getFieldsValue = formActionType.getFieldsValue;
    },
    render: () => {
      return h(SearchTable, {
        config: schemaItem,
        modalFn: { setFieldsValue, getFieldsValue },
      });
    },
  };
};
export const setPiDetailsSchemas = (schemaItem, data) => {
  piDetailsConfig['field'] = schemaItem?.field;
  piDetailsConfig['piid'] = data?.data?.id;
  piDetailsConfig['ifDraft'] = false;

  return {
    component: 'Input',
    render: () => {
      return h(PiDetailsTable, {
        piDetailsConfig,
      });
    },
  };
};

/** 插槽 */
export const setDynamictabSchemas = (schemaItem) => {
  // 存储一对多的columns
  dynamictabConfig.value[`${schemaItem.field}`] = {
    columns: schemaItem.cols,
    showActionBtn: schemaItem?.showActionBtn,
  };
  return {
    slot: 'dynamictab',
    colProps: { span: 24 },
  };
};
export const setFileSchemas = (schemaItem) => {
  // 存储columns
  fileConfig.value[`${schemaItem.field}`] = {
    successMessage: schemaItem?.successMessage,
    renderDrop: schemaItem?.renderDrop,
    accept: schemaItem?.accept,
    linkageDynamictab: schemaItem?.linkageDynamictab,
  };
  return {
    componentProps: {
      ...schemaItem,
    },
    slot: 'files',
  };
};

export const setCustomTableSchemas = (schemaItem, data) => {
  return {
    component: 'Input',
    render: () => {
      if (Reflect.has(schemaItem, 'api')) {
        return h(CustomTable, {
          api: schemaItem.api,
          columns: schemaItem.cols,
        });
      } else {
        return h(CustomTable, {
          columns: schemaItem.cols,
          dataSource: data.data[schemaItem.field],
        });
      }
    },
  };
};

export const setOverallTitleSchemas = (schemaItem, data) => {
  return {
    component: 'Input',
    render: () => {
      return h(OverallTitle, { data: data.data[schemaItem.field] });
    },
  };
};
