import { h } from 'vue';
import { DownOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import { Button, Dropdown, Menu, MenuItem } from 'ant-design-vue';

/**
 *
 * @param status
 * @returns 状态 :1未接收(默认)，2办理中 ，3已转交，4已同意
 */
export function processingStatusValue(status: number) {
  switch (status) {
    case 1:
      return 'wait';
    case 2:
      return 'process';
    case 3:
      return 'finish';
    case 4:
      return 'finish';
  }
}

/** 返回description */
export const descVnode = (data) => {
  return h('div', { style: { display: 'flex', justifyContent: 'space-between' } }, [
    h('div', { style: { width: '70%' } }, [
      // 文本
      h('div', null, 123123),
      // 附件
      true
        ? h('a', { style: { color: data.status !== 2 ? 'rgb(178, 180, 182' : '' } }, '这是附件')
        : '',
    ]),
    // 时间
    h(
      'span',
      {
        style: {
          paddingRight: '10px',
        },
      },
      data.created_at,
    ),
  ]);
};

/** 返回title */
export const titleVnode = (data, moreBtnArr = ['退回', '加签', '转交']) => {
  return h('div', { style: { width: '100%', display: 'flex', justifyContent: 'space-between' } }, [
    data.status === 2
      ? h('div', null, [data.title, h(LoadingOutlined, { style: { color: 'red' } })])
      : h('div', null, data.title),
    data.type === 'approver' && data.status === 2
      ? h('div', null, [
          h(
            Dropdown,
            {
              overlay: h(Menu, {}, () => {
                let arr: any = [];
                for (let item of moreBtnArr) {
                  arr.push(
                    h(
                      MenuItem,
                      {
                        onClick: (val: any) => {
                          let key = val.target.innerText;
                          switch (key) {
                            case '退回':
                              returned(data);
                              return;
                            case '加签':
                              countersigned(data);
                              return;
                            case '转交':
                              transmitted(data);
                              return;
                          }
                        },
                      },
                      () => h('a', {}, item),
                    ),
                  );
                }
                return arr;
              }),
            },
            () => [
              h(
                Button,
                {
                  size: 'small',
                  style: { marginLeft: '10px' },
                  onClick: handleClick,
                },
                ['更多', h(DownOutlined, null, () => null)],
              ),
            ],
          ),
          h(
            Button,
            {
              size: 'small',
              type: 'primary',
              danger: true,
              style: { marginLeft: '10px' },
              onClick: hanldleNo.bind(null, data),
            },
            '驳回',
          ),
          h(
            Button,
            {
              size: 'small',
              type: 'success',
              style: { danger: true, marginLeft: '10px' },
              onClick: hanldleOk.bind(null, data),
            },
            () => data.allow_btn_text,
          ),
        ])
      : '',
  ]);
};

const handleClick = (event) => {
  event.preventDefault();
};

/** 退回 */
const returned = (data) => {
  console.log('退回', data);
};

/** 加签 */
const countersigned = (data) => {
  console.log('加签', data);
};

/** 转交 */
const transmitted = (data) => {
  console.log('转交', data);
};

/** 同意 */
const hanldleOk = (data) => {
  console.log('同意', data);
};

/** 驳回 */
const hanldleNo = (data) => {
  console.log('驳回', data);
};
