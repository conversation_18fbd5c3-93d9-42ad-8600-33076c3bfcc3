<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Tabs v-model:activeKey="activeKey" @change="tabsChange">
          <TabPane key="1" tab="待处理" />
          <TabPane key="2" tab="已处理" />
        </Tabs>
      </template>
      <template #toolbar>
        <!-- <Button type="primary" @click="handleCreate">
            <template #icon>
              <PlusOutlined />
            </template>
            新增
          </Button> -->
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record, 1)" />
        </template>
      </template>

      <template #expandedRowRender="{ record }">
        <BasicTable @register="registerChildTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="createActions(record, 2)" />
            </template>
          </template>
        </BasicTable>
      </template>
    </BasicTable>

    <DetailsModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { message, Tabs, TabPane } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import type { ActionItem, EditRecordRow } from '@/components/Table';
  import { tableColumnsFn } from './datas/data';
  import { ref } from 'vue';
  import { getList, deleteSetting } from '@/api/approval/approval';
  import { useModal } from '@/components/Modal';
  import DetailsModal from './components/DetailsModal.vue';
  import { getRelPageList } from '/@/api/management/rel';

  const activeKey = ref('1');

  /** 注册表格 */
  const [registerTable, { reload, setProps, setLoading }] = useTable({
    showIndexColumn: false,
    columns: tableColumnsFn(),
    api: getList,
    rowKey: 'id',
    dataSource: [{ name: 123123, system: 123123 }],
    actionColumn: true
      ? {
          width: 300,
          title: '操作',
          dataIndex: 'action',
        }
      : void 0,
    immediate: true,
    showTableSetting: true,
    useSearchForm: true,
    formConfig: {
      labelWidth: 120,
      actionColOptions: {
        span: 24,
      },
      baseColProps: {
        span: 6,
      },
      schemas: [],
    },
  });

  /** 注册子表格 */
  const [registerChildTable, {}] = useTable({
    showIndexColumn: false,
    columns: tableColumnsFn(),
    api: getList,
    rowKey: 'id',
    dataSource: [{ name: 123123, system: 123123 }],
    actionColumn: true
      ? {
          width: 300,
          title: '操作',
          dataIndex: 'action',
        }
      : void 0,
    immediate: true,
    showTableSetting: false,
    useSearchForm: false,
    pagination: false,
    canResize: false,
  });

  /** 注册Modal */
  const [registerModal, { openModal }] = useModal();

  /** 操作按钮 */
  function createActions(record: EditRecordRow, type: number): Recordable[] {
    let buttonList: ActionItem[] = [];
    if (type === 1) {
      buttonList = [
        {
          icon: 'ant-design:eye-outlined',
          label: '查看',
          onClick: handleDetail.bind(null, record),
        },
        {
          icon: 'ant-design:delete-outlined',
          label: '删除',
          color: 'error',
          popConfirm: {
            title: '是否确认删除',
            placement: 'left',
            confirm: handleDelete.bind(null, record),
          },
        },
      ];
    } else {
      buttonList = [
        {
          icon: 'ant-design:notification-outlined',
          label: '催审',
          onClick: handleDetail.bind(null, record),
        },
      ];
    }

    return buttonList;
  }

  /** tabs change事件 */
  const tabsChange = (key) => {
    try {
      setLoading(true);
      console.log(key);
      if (key == '1') {
        setProps({ api: getList, searchInfo: { name: 1 } });
      } else {
        setProps({ api: getList, searchInfo: { name: 2 } });
      }
      reload();

      // 查询后需要重置为空
      setProps({ api: getList, searchInfo: {} });

      setTimeout(() => {
        setLoading(false);
      }, 700);
    } catch (error) {
      console.log(error);
    }
  };

  /** 查看 */
  function handleDetail(record) {
    openModal(true, {
      record: record,
    });
  }

  /** 删除 */
  async function handleDelete(record) {
    try {
      await deleteSetting({ id: record.id });
      message.success('删除成功!');
      reload();
    } catch (error) {
      console.log(error);
      message.success('删除失败!');
    }
  }

  /**  回调 */
  function handleSuccess() {
    reload();
  }
</script>

<style lang="less" scoped></style>
