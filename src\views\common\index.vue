<template>
  <PageWrapper dense contentFullHeight fixedHeight contentClass="flex" id="pageRef">
    <template #extra>
      <!-- 添加按钮 -->
      <Button
        v-show="create_btn"
        type="success"
        @click="
          allApiFn.handleCreate({
            _type: 'add',
            schema: getData?.nav?.form['main'] || getData?.nav?.form,
            data: { ifReload: true },
            _key: 'main',
          })
        "
      >
        <template #icon>
          <PlusCircleOutlined />
        </template>
        添加
      </Button>
      <!-- 顶部自定义按钮 -->
      <template v-for="(item, index) in TopBtnList">
        <template v-if="item.type == 'confirm'">
          <PopConfirmButton
            type="primary"
            placement="bottom"
            :bgColor="item.bgColor ?? ''"
            :title="item?.params?.tips ?? '确认执行此操作吗？'"
            @confirm="
              allApiFn.handlePopConfirmclick({
                title: item.title,
                _type: item.type,
                data: { ifReload: true },
                _key: item.key,
                schema: item.schema,
                params: item.params,
              })
            "
          >
            {{ item.title }}
          </PopConfirmButton>
        </template>
        <template v-else-if="item.type == 'exportApi'">
          <PopConfirmButton
            type="primary"
            placement="bottom"
            :bgColor="item.bgColor ?? ''"
            :title="item?.params?.tips ?? '确认执行此操作吗？'"
            @confirm="
              allApiFn.handleTopclick({
                title: item.title,
                _type: item.type,
                data: { ifReload: true },
                _key: item.key,
                schema: item.schema,
                params: item.params,
              })
            "
          >
            {{ item.title }}
          </PopConfirmButton>
        </template>
        <template v-else>
          <Button
            type="primary"
            :style="{ backgroundColor: item.bgColor }"
            @click="
              allApiFn.handleTopclick({
                title: item.title,
                _type: item.type,
                data: { ifReload: true },
                _key: item.key,
                schema: item.schema,
                params: item.params,
              })
            "
          >
            {{ item.title }}
          </Button>
        </template>
      </template>
      <!-- END -->
      <Badge :count="badgeCount">
        <Button type="primary" @click="handleSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          搜索
        </Button>
      </Badge>
      <Button @click="handleReset">
        <template #icon>
          <UndoOutlined />
        </template>
        重置
      </Button>
    </template>
    <template #headerContent>
      <Tabs
        default-active-key="detail"
        v-model:activeKey="currentKey"
        @change="tabItemSelected"
        size="small"
        tabPosition="top"
      >
        <TabPane v-for="item in getData?.nav?.tabs" :key="item.value" :tab="tabNode(item)" />
      </Tabs>
    </template>

    <BasicTable
      @register="registerTable"
      :loading="loading"
      :pagination="{ pageSize: pageSize }"
      @change="changePage"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
      <template>
    </BasicTable>

    <DetailDrawer
      @register="registerDrawer"
      @success="handleSuccess"
      @close-drawer="closeDrawer"
      ref="detailDrawerRef"
    />
    <!-- 单个按钮 -->
    <FloatButton
      v-if="FloatBtnList.length <= 1"
      v-for="(item, index) in FloatBtnList"
      :tooltip="item.title"
      type="primary"
      @click="
        allApiFn.handleCreate({
          _type: item.type,
          schema: getData?.nav?.form['main'],
          data: { ifReload: true },
          _key: item.key,
        })
      "
    >
      <template #icon>
        <PlusOutlined :style="{ color: item.color }" />
      </template>
    </FloatButton>
    <!-- 多个按钮 -->
    <FloatButton.Group
      v-if="FloatBtnList.length > 1"
      type="primary"
      key="top"
      trigger="hover"
      placement="top"
    >
      <template #icon>
        <PlusOutlined />
      </template>
      <FloatButton
        v-for="(item, index) in FloatBtnList"
        :tooltip="item.title"
        @click="
          allApiFn.handleCreate({
            _type: item.type,
            data: { ifReload: true },
            _key: item.key,
            schema: item.schema,
          })
        "
      >
        <template #icon>
          <PlusOutlined :style="{ color: item.color }" />
        </template>
      </FloatButton>
    </FloatButton.Group>

    <EditModal @register="registerEditModal" @success="handleSuccess" />
    <SearchModal @register="registerSearchModal" @handle-search-call-back="handleSearchCallBack" />
    <CustomExportModal
      @register="registerExportModal"
      @handle-search-call-back="handleExportCallBack"
    />
    <!-- 导入model -->
    <ImpExcelModal
      v-loading
      @register="registerUploadModal"
      :dataCallBackFn="handleUploadData"
      @success="onUploadSuccess"
      @error="onUploadError"
      ref="excelModalRef"
    />
    <!-- 导入 -->
    <ImpLinkageExcelModal
      v-loading
      @register="registerUploadLinkModal"
      :dataCallBackFn="handleUploadLinkageData"
      @success="onUploadLinkageSuccess"
      @error="onUploadLinkageError"
      ref="excelLinkageModalRef"
    />
    <CustomImportModal
      @register="registerCustomImportModal"
      :afterClose="handleCloseCustomImportModal"
    />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { onMounted, ref, h } from 'vue';
  import { Tabs, TabPane, Button, Badge, FloatButton, message } from 'ant-design-vue';
  import { PopConfirmButton } from '@/components/Button';
  import { PageWrapper } from '@/components/Page';
  import { useDrawer } from '@/components/Drawer';
  import DetailDrawer from '@/views/components/crm/DetailDrawer.vue';
  // import { listAction, createAction, deleteAction } from '@/api/admin/dealt/customers';
  import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '@/components/Table';
  import SearchModal from '@/views/components/crm/SearchModal.vue';
  import CustomExportModal from '@/views/components/crm/CustomExportModal.vue';
  import { useModal } from '@/components/Modal';
  import { keys, isObject, get, cloneDeep, omit, pick } from 'lodash-es';
  import EditModal from '@/views/components/crm/EditModal.vue';
  import CustomImportModal from '@/views/components/crm/CustomImportModal.vue';
  //导入表格
  import { ImpExcelModal, ImpLinkageExcelModal } from '@/components/Excel';

  //导入加载组件
  import { Loading, useLoading } from '/@/components/Loading';
  //导出组件
  import { aoaToSheetXlsx } from '/@/components/Excel';
  import {
    handleSuccessFn,
    mappingHeader,
    multipleChoices,
    tableActionFn,
  } from '@/views/components/datas/common';
  import { useRouter, useRoute } from 'vue-router';
  import {
    PlusOutlined,
    PlusCircleOutlined,
    SearchOutlined,
    UndoOutlined,
  } from '@ant-design/icons-vue';
  import { getExportData } from '@/api/common';

  const [openFullLoading, closeFullLoading] = useLoading({
    tip: '加载中...',
  });

  const router = useRouter();
  const route = useRoute();
  let routePath: any = router.currentRoute.value.fullPath;
  const apiFn = ref();

  const loading = ref(true);
  const currentKey = ref(0);
  const pageSize = ref(10);
  const currentPage = ref(1); //当前组件
  const recordData = ref();
  const badgeCount = ref(0);
  const searchParams: any = ref(null);
  const exportParams: any = ref(null);
  const detailDrawerRef: any = ref(null);
  const getData: any = ref();
  let create_btn = ref(false);
  const editPersonInChargeStatus = ref(true);
  let flag = ref(false); // 标志抽屉内是否有修改，关闭抽屉时需要刷新外层
  let outerFlag = ref(false); // 没进去抽屉，直接外层修改，需要刷新外层

  /** 注册抽屉 */
  const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer();

  /** 注册查询Modal */
  const [registerSearchModal, { openModal: openSearchModal, setModalProps: setSearchModalProps }] =
    useModal();

  // 注册导出modal
  const [registerExportModal, { openModal: openExportModal, setModalProps: setExportModalProps }] =
    useModal();

  // 注册自定义导入modal
  const [
    registerCustomImportModal,
    { openModal: openCustomImportModal, setModalProps: setCustomImportModalProps },
  ] = useModal();

  /** 注册Modal */
  const [registerEditModal, { openModal, setModalProps }] = useModal();
  /** 注册导入 Model*/
  const [registerUploadModal, { openModal: openUploadModel }] = useModal();
  /** 注册导入 Model*/
  const [registerUploadLinkModal, { openModal: openUploadLinkModel }] = useModal();

  /** 注册表格 */
  const [
    registerTable,
    { setTableData, setColumns, setPagination, getSelectRows, clearSelectedRowKeys, setProps },
  ] = useTable({
    showTableSetting: true,
    bordered: true,
    showIndexColumn: false,
    isTreeTable: true,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  let allApiFn = {
    /** 跟进 */
    handleFollowUp: (record: Recordable) => {
      recordData.value = record;
      flag.value = false;
      outerFlag.value = false;
      openDrawer(true, {
        record,
        apiFn: apiFn.value,
        columns: getData.value?.nav?.columns,
        nav: getData.value?.nav,
        params: { tabIndex: currentKey.value },
      });

      let drawerTitle =
        multipleChoices(
          getData.value?.nav?.columns[0],
          record[getData.value?.nav?.columns[0].field],
        ) || '-';
      setDrawerProps({
        title: `${getData.value?.nav?.columns[0].title}-${isObject(drawerTitle) ? drawerTitle?.props?.innerHTML : drawerTitle}`,
      });
    },

    /** 创建 编辑*/
    handleCreate: (options) => {
      console.log('handleCreate', options);
      recordData.value = options.data;
      let { _title } = options;

      setModalProps({ title: _title || '创建' });

      outerFlag.value = false;
      if (Reflect.has(options.data, 'ifReload')) {
        outerFlag.value = true;
      }
      console.log(options, 'options');

      openModal(true, options);
      // openModal(true, { ...options, _tabIndex: currentKey.value });
    },
    /** 顶部按钮点击*/
    handleTopclick: (options) => {
      console.log('handleTopclick:', options);
      recordData.value = options.data;
      let { title } = options;
      setModalProps({ title: title || '操作' });
      outerFlag.value = false;
      if (Reflect.has(options.data, 'ifReload')) {
        outerFlag.value = true;
      }
      if (options._type == 'import') {
        //如果是导入
        openUploadModel(true, {
          sheetName: options?.params?.sheetName ?? 'sheet1',
          headerRow: options?.params?.headerRow ?? 1,
          startCell: options?.params?.startCell ?? 'A',
          endCell: options?.params?.endCell ?? 'H',
          downloadUrl: options?.params?.downloadUrl ?? '',
        });
      } else if (options._type == 'importLink') {
        //如果是关联导入
        openUploadLinkModel(true, {
          sheetName: options?.params?.sheetName ?? 'sheet1',
          headerRow: options?.params?.headerRow ?? 1,
          startCell: options?.params?.startCell ?? 'A',
          endCell: options?.params?.endCell ?? 'H',
          downloadUrl: options?.params?.downloadUrl ?? '',
        });
      } else if (options._type === 'customImport') {
        setCustomImportModalProps({ title: '自定义导入' });
        openCustomImportModal(true, {
          options: options.params.columns,
          importsApi: options.params.uploadUrl || apiFn.value.importExcelAction,
          defaultOptions: (options.params ?? {}).defaultColumns ?? [],
        });
      } else if (options._type == 'export') {
        let navData = getData.value.nav ?? {};
        let _key = options?._key ?? false;
        let excelData = any();
        if (Object.keys(navData).includes(_key)) {
          excelData = navData[_key];
        }
        console.log('excelData:', excelData);
        aoaToSheetXlsx({
          data: excelData?.data ?? {},
          header: excelData?.header ?? {},
          filename: `${options.title}.xlsx`,
        });
      } else if (options._type === 'customExport') {
        setExportModalProps({ title: '导出条件' });
        openExportModal(true, {
          schema: { schema: getData.value.nav.export_columns },
          data: exportParams.value,
          _type: 'edit',
          url: options?.params?.url,
          title: options.title,
        });
      } else if (options._type == 'exportApi') {
        //根据API获取数据导出
        getExportData(options?.params?.url, {}).then((res) => {
          aoaToSheetXlsx({
            data: res?.data ?? [],
            header: res?.header ?? [],
            filename: `${options.title}.xlsx`,
          });
        });
      } else {
        openModal(true, options);
      }
    },
    /** 顶部[气泡]按钮点击*/
    async handlePopConfirmclick(options) {
      console.log('点击了气泡', options);
      let confirmType = options?.params?.confirm_type ?? '';
      if (confirmType == 'search') {
        var params = {
          _key: options._key,
          ...fromQuery,
        };
        let res = await apiFn.value.createAction(params);
        if (res) {
          message.success(res.msg);
          initTab();
        }
      } else {
        var params = {
          _key: options._key,
          ...fromQuery,
        };
        let res = await apiFn.value.createAction(params);
        if (res) {
          message.success(res.msg);
          initTab();
        }
      }
    },
    // 每行点击的popConfirm
    async handlePopConfirmRecord(options: { _type: string; _key: string; _id: number }) {
      apiFn.value.deleteAction({ ...options }).finally(() => {
        initTab();
      });
    },
  };
  const excelLinkageModalRef = ref<InstanceType<typeof ImpLinkageExcelModal>>();
  //dataCallBackFn-导入数据回调-多表
  function handleUploadLinkageData(data) {
    console.log('dataCallBackFn:', data);
    if (data.length <= 0) {
      return false;
    }
    setTimeout(function () {
      excelLinkageModalRef.value.showLoading();
    }, 200);
    apiFn.value
      .importExcelAction({
        items: data,
        _key: 'main',
      })
      .then((res) => {
        console.log('res:', res);
        var tips = {};
        if (res.success) {
          tips = {
            title: '导入成功',
            subTitle: '导入已经完成。请勿重复导入',
            status: 'success',
          };
        }
        callHandleLinkageStepNext(tips);
        onUploadLinkageSuccess();
      })
      .catch((err) => {
        onUploadLinkageError();
        console.error(err);
      });
    //拦截返回结果
    return false;
  }
  //onUploadLinkageSuccess
  function onUploadLinkageSuccess(msg = '') {
    console.log('onUploadLinkageSuccess:', msg);
    //刷新表格
    initTab();
    if (excelLinkageModalRef.value && excelLinkageModalRef.value.hideLoading) {
      excelLinkageModalRef.value.hideLoading();
    }
  }
  function onUploadLinkageError() {
    console.log('onUploadLinkageError');
    if (excelLinkageModalRef.value && excelLinkageModalRef.value.hideLoading) {
      excelLinkageModalRef.value.hideLoading();
    }
  }

  //导入成功-切换到最后一步
  const callHandleLinkageStepNext = (tips) => {
    if (excelLinkageModalRef.value && excelLinkageModalRef.value.handleChangeTips && tips) {
      //修改显示的文本
      excelLinkageModalRef.value.handleChangeTips(tips);
    }
    if (excelLinkageModalRef.value && excelLinkageModalRef.value.handleStepNext) {
      //显示最后一步
      excelLinkageModalRef.value.handleStepNext();
    }
  };
  /********************************/

  const excelModalRef = ref<InstanceType<typeof ImpExcelModal>>();
  //dataCallBackFn-导入数据回调-单表
  function handleUploadData(data) {
    console.log('dataCallBackFn:', data);
    if (data.length <= 0) {
      return false;
    }
    setTimeout(function () {
      excelModalRef.value.showLoading();
    }, 200);
    apiFn.value
      .importExcelAction({
        items: data,
        _key: 'main',
      })
      .then((res) => {
        console.log('res:', res);
        var tips = {};
        if (res.success) {
          tips = {
            title: '导入成功',
            subTitle: '导入已经完成。请勿重复导入',
            status: 'success',
          };
        }
        callHandleStepNext(tips);
        onUploadSuccess();
      })
      .catch((err) => {
        onUploadError();
        console.error(err);
      });
    //拦截返回结果
    return false;
  }
  //onUploadSuccess
  function onUploadSuccess(msg = '') {
    console.log('onUploadSuccess:', msg);
    //刷新表格
    initTab();
    if (excelModalRef.value && excelModalRef.value.hideLoading) {
      excelModalRef.value.hideLoading();
    }
  }
  function onUploadError() {
    console.log('onUploadError');
    if (excelModalRef.value && excelModalRef.value.hideLoading) {
      excelModalRef.value.hideLoading();
    }
  }

  //导入成功-切换到最后一步
  const callHandleStepNext = (tips) => {
    if (excelModalRef.value && excelModalRef.value.handleChangeTips && tips) {
      //修改显示的文本
      excelModalRef.value.handleChangeTips(tips);
    }
    if (excelModalRef.value && excelModalRef.value.handleStepNext) {
      //显示最后一步
      excelModalRef.value.handleStepNext();
    }
  };

  function createBtn(config): ActionItem {
    const { btnConfig } = pick(config, 'btnConfig');
    const { popConfirm } = pick(cloneDeep(config), 'popConfirm');
    const mapBtnTemp = {
      delete: {
        label: config.label || config.title,
        icon: config.icon,
        color: 'error',
        popConfirm: {
          title: '是否确认删除？',
          confirm: allApiFn.handlePopConfirmRecord.bind(null, config),
          ...popConfirm,
        },
        ...omit(btnConfig, 'type'),
      },
    };
    if (mapBtnTemp[config._type]) {
      return mapBtnTemp[config._type];
    }
    return {
      label: config.label || config.title,
      icon: config.icon,
      onClick: () => {
        allApiFn.handleCreate(config);
      },
      ...btnConfig,
    };
  }

  /** 操作按钮 */
  function createActions(record: EditRecordRow): Recordable[] {
    let editButtonList: any[] = [];
    editButtonList = [
      {
        label: '详情',
        icon: 'clarity:clipboard-outline-badged',
        onClick: allApiFn.handleFollowUp.bind(null, record),
      },
    ];
    //循环添加操作按钮
    if (record?.actionBtnList) {
      console.log('record?.actionBtnList:', record?.actionBtnList);
      record?.actionBtnList.forEach((item) => {
        let navData = getData.value.nav ?? {};
        let _key = item?.key ?? false;
        let formData = getData.value.nav?.form[item.key] || getData.value.nav?.form;
        if (Object.keys(navData).includes(_key)) {
          formData = navData[_key];
        }

        let btn = createBtn({
          title: item.label,
          _type: item.type,
          schema: formData,
          data: { ifReload: true },
          _key: item.key,
          _id: record.id,
          popConfirm: record.popConfirm ?? {},
          btnConfig: item,
        });
        editButtonList.push(btn);
      });
    }
    editButtonList = editButtonList.concat(
      tableActionFn({
        url: routePath.match(/^\/(\w+)/),
        data: record,
        schema: getData.value?.nav?.form,
        fn: {
          ...allApiFn,
        },
      }) || [],
    );
    return editButtonList;
  }

  /** 初始化 */
  onMounted(async () => {
    let allApiField = import.meta.glob(`@/api/common/*/*.ts`);
    apiFn.value = await allApiField[`/src/api/common${routePath}.ts`]();
    apiFn.value = { ...apiFn.value };
    await initTab();
  });

  /** 返回tab */
  const tabNode = (data) => {
    return h(
      Badge,
      {
        count: data.num,
        offset: [8, 0],
        numberStyle: {
          height: '15px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '10px',
        },
      },
      () => data.name,
    );
  };
  //将搜索条件保存起来
  let fromQuery = new Object();
  async function initTab(params?) {
    try {
      loading.value = true;
      let query = {
        tabIndex: currentKey.value,
        page: currentPage.value,
        pageSize: pageSize.value,
        ...params,
        ...searchParams.value,
      };
      //更新页面搜索条件
      fromQuery = query;
      let res = await apiFn.value.listAction(query);
      getData.value = res;

      let { nav, items } = res;
      setColumns(mappingHeader(nav.columns));
      setTableData(items);
      setPagination(res);
      //设置浮动按钮数组
      // setFloatBtn(getData.value?.nav?.create_btn);
      //显示添加按钮
      create_btn.value = getData.value?.nav?.create_btn;
      //设置顶部按钮数组
      setTopBtn(getData.value?.nav?.topBtnList ?? []);
      //判断是否需要显示多选列
      if (getData.value?.nav?.rowSelection) {
        setProps({ rowSelection: true });
      }
      //动态设置操作栏宽度
      setActionCloumnWidth();
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  }
  /** 动态设置操作栏宽度 */
  function setActionCloumnWidth() {
    let width = getData.value?.nav?.actionWidth ?? 150;
    var tmp_actionColumn = {
      width,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    };
    setProps({ actionColumn: tmp_actionColumn });
  }
  /** tab切换事件 */
  async function tabItemSelected() {
    clearSelectedRowKeys();
    await initTab({ page: 1, pageSize: pageSize.value });
    setPagination({
      current: 1,
      pageSize: pageSize.value,
    });
  }

  /** 选中 */
  function handleChange() {
    if (getSelectRows().length == 0) {
      editPersonInChargeStatus.value = true;
    } else {
      editPersonInChargeStatus.value = false;
    }
  }

  /** 关闭抽屉回调 */
  const closeDrawer = async () => {
    recordData.value = null;
    if (flag.value) {
      await initTab();
    }
  };

  /** 分页跳转 */
  async function changePage(res: any) {
    currentPage.value = res.current;
    pageSize.value = res.pageSize;
    await initTab();
  }

  /** 搜索 */
  const handleSearch = () => {
    setSearchModalProps({ title: '搜索条件' });
    openSearchModal(true, {
      badgeCount: badgeCount.value,
      schema: { schema: getData.value.nav.search_columns },
      data: searchParams.value,
      _type: 'edit',
    });
  };

  /** 重置 */
  const handleReset = async () => {
    badgeCount.value = 0;
    searchParams.value = null;
    clearSelectedRowKeys();
    await initTab({
      page: 1,
      pageSize: pageSize.value,
    });
    setPagination({
      current: 1,
      pageSize: pageSize.value,
    });
  };

  /** 搜索回调 */
  const handleSearchCallBack = async (params) => {
    let newParams = {};
    searchParams.value = params;
    console.log('handleSearchCallBack:', params);
    badgeCount.value = 0;
    keys(params).forEach((item) => {
      if ((params[item] && params[item] !== '') || params[item] === 0) {
        badgeCount.value++;
        newParams[item] = params[item];
      } else {
        newParams[item] = null;
      }
    });
    newParams = {
      ...params,
      page: 1,
      pageSize: pageSize.value,
    };
    await initTab(newParams);
    setPagination({
      current: 1,
      pageSize: pageSize.value,
    });
  };

  /** 导出回调 */
  const handleExportCallBack = async (params, opt) => {
    let newParams = {};
    exportParams.value = params;
    keys(params).forEach((item) => {
      if ((params[item] && params[item] !== '') || params[item] === 0) {
        newParams[item] = params[item];
      } else {
        newParams[item] = null;
      }
    });
    newParams = {
      ...params,
    };

    // console.log('handleExportCallBack:', newParams, opt);
    console.log(route);

    getExportData(opt?.url, newParams).then((res) => {
      aoaToSheetXlsx({
        data: res?.data ?? [],
        header: res?.header ?? [],
        // filename: `${options.title}.xlsx`,
        filename: `${route.meta.title}-${+new Date()}.xlsx`,
      });
    });
  };

  /** 回调 */
  async function handleSuccess(datas) {
    try {
      await handleSuccessFn(
        {
          ...datas,
          tabIndex: currentKey.value,
        },
        datas._id || recordData.value?.id, // 如果有指定外层_id,那么就以指定的为准，如果没有就以列表页id为准
        apiFn.value,
        detailDrawerRef.value.initDetail,
      );
      flag.value = true;
      if (outerFlag.value) {
        await initTab();
      }
    } catch (error) {
      console.log(error);
      message.error('操作失败！');
    } finally {
      datas.setOkBtn(false);
      datas.changeLoading(false);
    }
  }
  let FloatBtnList: any = ref([]);
  /*自定义浮动按钮*/
  function setFloatBtn(btnList) {
    FloatBtnList.value = [];
    if (!btnList) return false;
    if (btnList === true) {
      var btn = new Object({
        title: '添加',
        right: '24px',
        type: 'add',
        key: 'main',
        color: '#fff',
        schema: getData.value?.nav?.form,
      });
      FloatBtnList.value.push(btn);
    } else {
      // console.log('数组：',btnList);
      btnList.forEach(function (item, index) {
        let navData = getData.value.nav ?? {};
        let _key = item?.key ?? false;
        let formData = getData.value.nav?.form ?? {};
        // console.log("navData:",navData);
        // console.log("formData:",formData);
        // console.log("_key:",_key);
        if (Object.keys(navData).includes(_key)) {
          formData = navData[_key];
        }
        // console.log("formData:",formData);
        let btn = new Object({
          title: item?.title,
          right: ((index + 1) * 48).toString() + 'px',
          type: item?.type ?? 'add',
          key: item?.key,
          color: item?.bgColor ?? '#2a7dc9',
          schema: formData,
        });
        FloatBtnList.value.push(btn);
      });
    }
    console.log('FloatBtnList:', FloatBtnList);
  }
  let TopBtnList: any = ref([]);
  const setTopBtn = (btnList) => {
    TopBtnList.value = [];
    if (btnList.length <= 0) return false;
    console.log('顶部按钮：', btnList);
    btnList.forEach(function (item, index) {
      let navData = getData.value.nav ?? {};
      let _key = item?.key ?? false;
      let formData = getData.value.nav?.form ?? {};
      if (Object.keys(navData).includes(_key)) {
        formData = navData[_key];
      }
      console.log('formData:', formData);
      let btn = new Object({
        title: item?.title,
        right: ((index + 1) * 48).toString() + 'px',
        type: item?.type ?? 'add',
        key: item?.key,
        bgColor: item?.bgColor ?? '#2a7dc9',
        schema: formData,
        params: item?.params ?? {},
      });
      TopBtnList.value.push(btn);
    });
    console.log('TopBtnList:', TopBtnList);
  };

  function handleCloseCustomImportModal() {
    initTab();
    return true;
  }

  // /** 弹窗点击确定回调 */
  // async function handleEditModalSuccess(datas) {
  //   try {
  //     await handleSuccessFn(
  //       datas,
  //       datas.id,
  //       initTab,
  //       apiFn.value,
  //       detailDrawerRef.value.initDetail,
  //     );
  //   } catch (error) {
  //     console.log(error);
  //     datas.changeLoading(false);
  //     message.error('操作失败！');
  //   }
  // }
</script>
