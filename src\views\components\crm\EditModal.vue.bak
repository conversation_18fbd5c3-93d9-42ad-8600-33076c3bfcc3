<template>
  <BasicModal @register="registerEditModal" width="90%" destroyOnClose>
    <BasicForm @register="registerForm">
      <!-- 报价单详情 -->
      <template #pimsProduct>
        <FormItemRest>
          <PiDetailsTable :piid="propsData.data.id" />
        </FormItemRest>
      </template>

      <!-- 动态表单 -->
      <template #dynamictab="{ field }">
        <FormItemRest>
          <Dynamictab
            :data="propsData.data[field]"
            :config="dynamictabConfig[field]"
            :type="propsData._type"
            :field="field"
            :modalFn="{
              setFieldsValue: setFieldsValue,
              updateSchema: updateSchema,
            }"
            @handle-ok-disabled="handleOkDisabled"
            :key="propsData.data[field]"
          />
        </FormItemRest>
      </template>

      <!-- 附件上传 -->
      <template #files="{ field }">
        <UploadFile
          :data="propsData"
          :field="field"
          :modalFn="{
            changeLoading: changeLoading,
            setFieldsValue: setFieldsValue,
            setModalProps: setModalProps,
          }"
          @success="hangdleUploadSuccess"
        />
      </template>
      <!-- 任意附件上传 -->
      <template #anyfile="{ field }">
        <UploadAnyFile
          :data="propsData"
          :field="field"
          :modalFn="{
            changeLoading: changeLoading,
            setFieldsValue: setFieldsValue,
            setModalProps: setModalProps,
          }"
          @success="hangdleUploadSuccess"
        />
      </template>

      <!-- 模板下载 -->
      <template #download="{ model, field }"
        ><a :href="model[field]" target="_ blank">下载</a></template
      >

      <!-- 快捷模板 -->
      <template #quick="{ model, field }">
        <Quick
          :modelValue="model[field]"
          :field="field"
          :modalFn="{
            setFieldsValue: setFieldsValue,
          }"
        />
      </template>

      <!-- 选择颜色的redio -->
      <template #radioColor="{ model, field }">
        <RadioColor
          :config="colorConfig[field]"
          :modelValue="model[field]"
          :field="field"
          :modalFn="{
            setFieldsValue: setFieldsValue,
          }"
        />
      </template>

      <!-- 联动 -->
      <template #linkage="{ model, field }">
        <Linkage
          :modelValue="model[field]"
          :config="linkageConfig[field]"
          :field="field"
          :modalFn="{
            setFieldsValue: setFieldsValue,
          }"
        />
      </template>

      <!-- 销售单号选择 -->
      <template #saleOrderSel="{ model, field }">
        <saleOrderSel
          :modelValue="model[field]"
          :field="field"
          :modalFn="{
            setFieldsValue: setFieldsValue,
          }"
          :config="tableSlotConfig[field]"
          :dataSource="propsData.data"
        />
      </template>
      <template #tableSlot="{ field }">
        <!-- 如果dataSource为空数组，则在组件里面调取接口，如果是调取接口，表头也用接口返回的表头，而不是用配置里面的cols表头 -->
        <TableSlot
          :dataSource="propsData.data[field]"
          :columns="tableSlotConfig[field].cols"
          :modelValue="propsData.data?.process?.form_value?._pi"
          :config="tableSlotConfig[field]"
          :api="propsData.data?.api"
          :apiParams="propsData.data?.apiParams"
          :field="field"
          :modalFn="{
            setFieldsValue: setFieldsValue,
          }"
        />
      </template>

      <!-- sop详情 -->
      <template #approvalNav>
        <ApprovalNav :data="propsData" :ifShowAction="false" />
      </template>

      <template #inputGroup="{ model, field }">
        <InputGroupSlot
          :modelValue="model[field]"
          :field="field"
          :modalFn="{
            setFieldsValue: setFieldsValue,
            clearValidate: clearValidate,
          }"
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
  import { useForm, BasicForm } from '@/components/Form';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import { ref } from 'vue';
  import { debounce, cloneDeep, pick, assign, throttle } from 'lodash-es';
  import PiDetailsTable from '@/views/components/crm/form/PiDetailsTable.vue';
  import { Form } from 'ant-design-vue';
  import {
    schemaFn,
    // dynamictabConfig,
    // linkageConfig,
    // colorConfig,
    // tableSlotConfig,
  } from '@/views/components/datas/editModal';

  import Dynamictab from '@/views/components/crm/form/Dynamictab.vue';
  import UploadFile from '@/views/components/crm/form/UploadFile.vue';
  import Quick from '@/views/components/crm/form/Quick.vue';
  import RadioColor from '@/views/components/crm/form/RadioColor.vue';
  import Linkage from '@/views/components/crm/form/Linkage.vue';
  import TableSlot from '@/views/components/crm/form/TableSlot.vue';
  import ApprovalNav from '@/views/components/crm/ApprovalNav.vue';
  import InputGroupSlot from '@/views/components/crm/form/InputGroupSlot.vue';
  import { exportTemplate } from '../datas/exportFn';
  import UploadAnyFile from '@/views/components/crm/form/UploadAnyFile.vue';
  import saleOrderSel from '@/views/components/crm/form/saleOrderSel.vue';

  const emit = defineEmits(['register', 'success', 'exportPDF']);
  const FormItemRest = Form.ItemRest;

  const propsData = ref();

  /** 注册 Form */
  const [
    registerForm,
    { resetSchema, setFieldsValue, resetFields, validate, clearValidate, updateSchema },
  ] = useForm({
    showActionButtonGroup: false,
  });

  /** 注册 Modal */
  const [registerEditModal, { closeModal, setModalProps, changeLoading, changeOkLoading }] =
    useModalInner(async (data) => {
      try {
        changeLoading(true);
        setModalProps({ loadingTip: '' });

        propsData.value = cloneDeep(data);
        console.log(data, '弹窗刚进来的数据');

        // sop查看报价单详情时需要导出
        if (data._type == 'detail' && Reflect.has(data.data, 'strid')) {
          setModalProps({
            showOkBtn: true,
            showCancelBtn: ['detail', 'edit', 'add'].includes(data._type),
            okText: '导出',
            okButtonProps: {
              disabled: false,
              onClick: async () => {
                await exportTemplate([data.data], '正在导出....');
              },
            },
          });
        } else {
          setModalProps({
            showOkBtn: ['edit', 'add', 'add_setValue'].includes(data._type),
            showCancelBtn: ['detail', 'edit', 'add', 'add_setValue'].includes(data._type),
            okText: '确认',
            okButtonProps: {
              disabled: false,
              onClick: () => {
                handleOk();
              },
            },
          });
        }

        await resetFields();
        if (data._type === 'edit' || data._type == 'detail' || data._type == 'add_setValue') {
          //data._type == 'add_setValue' 如果不加多这个类型的话，有一些表单在编辑时需要禁用掉，添加时又要放开的时候，add_setValue表示是添加类型，但是又要利用外部传入的数据去塞到某个表单内的数据
          await resetSchema(schemaFn(data));
          await setFieldsValue(assign(propsData.value.data, propsData.value.data?._params));
        } else {
          await resetSchema(schemaFn(data));
        }

        changeLoading(false);
      } catch (error) {
        console.log(error);
        changeLoading(false);
      }
    });

  /** 附件上传完成时联动Dynamictab*/
  const hangdleUploadSuccess = async (data) => {
    console.log(data, 'datadata');
    propsData.value.data[`${data.dynamictabField}_import`] = data.dataSource;

    // 单独处理导入报价单塞折前含佣金额和报价单名称
    await setFieldsValue({ before_commission_price: data.allAmount, title: data.fileName });
  };

  /** 确认按钮是否要disabled */
  const handleOkDisabled = (currentEditKeyRef: any) => {
    setModalProps({
      okButtonProps: {
        disabled: currentEditKeyRef == '' ? false : true,
      },
    });
  };

  /** 确定 */
  const _handleOk = async () => {
    console.log('点击了确认');
    let data = await validate();
    setModalProps({ loadingTip: '' });
    changeLoading(true);
    changeOkLoading(true);

    // 处理不需要的数据,这里塞有可能传过来的字段但是表单里面没有的
    let formartArr: any = ['_id', 'sort', ' _key', '_params', '_isSop'];
    propsData.value.schema?.forEach((item) => {
      item.dataItem.forEach((dataItem) => {
        formartArr.push(dataItem.field);
      });
    });

    let { _type, _key, _isSop, _id } = propsData.value;

    // 处理非必填字段无法清空选项的情况：字段清空了值后，整个字段被过滤了
    // 这里默认给没有值的字段一个null，让后端自己判断
    const defaultData = pick(propsData.value.data, formartArr);
    for (const key of Object.keys(defaultData)) {
      data[key] = data[key] ?? null;
    }

    let datas: any = {
      ...defaultData,
      ...data,
      ...propsData.value.data._params, // 接口配置的默认参数
      _type,
      _key,
      _isSop,
      _id, // 外层id
      changeLoading,
      changeOkLoading,
      closeModal,
      errorNoticeFn: propsData.value?.errorNoticeFn, // sop拖拽前置表单点击确认接口报错的回调
    };
    console.log(datas, '弹窗确定后传输的数据');

    emit('success', datas);
    datas = null;
  };
  const handleOk = throttle(_handleOk, 300);
</script>

<style lang="scss" scoped>
  div {
    background-color: #e1e6ec;
  }
</style>
