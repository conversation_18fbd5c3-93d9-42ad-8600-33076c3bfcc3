<template>
  <BasicDrawer @register="registerDrawer" showFooter :width="700">
    <!-- <div v-if="node"> -->
    <BasicForm @register="registerForm">
      <!-- <component :is="drawerComponents[node.nodeType]" :config="node.config" /> -->
    </BasicForm>
    <!-- </div> -->
    <template #footer>
      <Button @click="cancelUpdateConfig">取消</Button>
      <Button type="primary" @click="updateConfig">确定</Button>
    </template>
  </BasicDrawer>
</template>
<script setup name="BaseDrawer" lang="ts">
  import Button from '../../../Button/Button.vue';
  import { ref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { cloneDeep } from 'lodash-es';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { approverSchemaFn, conditionSchemaFn } from './data';

  let node = ref(null);

  const [registerForm, { getFieldsValue, resetSchema, resetFields, setFieldsValue, validate }] =
    useForm({
      labelWidth: 120,
      baseColProps: { span: 24 },
      showActionButtonGroup: false,
    });

  // const approverText: any = ref({})
  // const signerText: any = ref({})

  const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
    await resetFields();
    console.log(data);
    node.value = cloneDeep(data.node);

    // 不同类型配置不同scheme
    if (data.node.nodeType === 'condition') {
      await resetSchema(conditionSchemaFn(data.node.config.title));
    } else {
      await resetSchema(approverSchemaFn(true, data.node.config.title));
    }
    await setFieldsValue(data.node.config);
  });

  // // 加载节点抽屉组件
  // const modules = import.meta.glob('../*/*Drawer.vue')
  // const drawerComponents = shallowRef({})
  // Object.keys(nodeConfig).forEach((key) => {
  //   let item = nodeConfig[key]
  //   if (item.hasDrawer) {
  //     let component = defineAsyncComponent(modules[`../${key}/${key}Drawer.vue`])
  //     drawerComponents.value[key] = component
  //   }
  // })

  const emit = defineEmits(['updateConfig', 'cancelUpdateConfig']);

  // 更新节点配置数据
  const updateConfig = async () => {
    await validate();
    let submitData = getFieldsValue();
    // console.log({ ...submitData, approverText: approverText.value })
    emit('updateConfig', submitData);
    closeDrawer();
  };

  // 取消更新节点配置数据
  const cancelUpdateConfig = () => {
    emit('cancelUpdateConfig');
    closeDrawer();
  };
</script>

<style lang="less" scoped></style>
